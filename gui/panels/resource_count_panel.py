import wx
import wx.grid as gridlib
import re
import csv
from wx import adv

class ResourceCountPanel(wx.Panel):
    def __init__(self, parent, resource_manager):
        super().__init__(parent)
        self.resource_manager = resource_manager
        self.resource_list = []  # Will hold the resource data
        self.scanned_resources = set()  # Store scanned IDs here
        self.init_ui()

    def init_ui(self):
        # Create main sizer
        main_sizer = wx.BoxSizer(wx.VERTICAL)

        # Scanned Resource input
        self.scanned_input = wx.TextCtrl(self, style=wx.TE_PROCESS_ENTER)
        self.scanned_input.Bind(wx.EVT_TEXT_ENTER, self.on_scan_resource)
        main_sizer.Add(self.scanned_input, 0, wx.ALL | wx.EXPAND, 10)

        # Buttons for importing data
        button_sizer = wx.BoxSizer(wx.HORIZONTAL)
        import_button = wx.Button(self, label="Import Resource List")
        import_button.Bind(wx.EVT_BUTTON, self.on_import_csv)
        button_sizer.Add(import_button, 0, wx.ALL, 5)

        clipboard_button = wx.Button(self, label="Paste from Clipboard")
        clipboard_button.Bind(wx.EVT_BUTTON, self.on_paste_from_clipboard)
        button_sizer.Add(clipboard_button, 0, wx.ALL, 5)
        
        self.report_button = wx.Button(self, label="Show count report")
        self.report_button.Bind(wx.EVT_BUTTON, self.show_count_report)
        self.report_button.Disable()
        button_sizer.Add(self.report_button, 0, wx.ALL, 5)

        main_sizer.Add(button_sizer, 0, wx.CENTER)

        # Create the grid
        self.grid = gridlib.Grid(self)
        self.grid.CreateGrid(0, 3)  # Start with an empty grid
        self.grid.SetColLabelValue(0, "resourceID*")
        self.grid.SetColLabelValue(1, 'res_link')
        self.grid.SetColLabelValue(2, 'Title*')
        self.grid.SetColSize(0, 70)
        self.grid.SetColSize(1, 130)
        self.grid.SetColSize(2, 480)

        main_sizer.Add(self.grid, 1, wx.ALL | wx.EXPAND, 10)
        self.SetSizer(main_sizer)

    def on_scan_resource(self, event):
        self.report_button.Enable(True)
        scanned_id = self.scanned_input.GetValue().strip()
        self.scanned_input.Clear()  # Clear input after scanning
        match = re.match(r"rdar://res/(\d+)", scanned_id)
        if match:
            scanned_id = match.group(1)  # Extract the numeric ID from the link

        # Check if the scanned ID is in the resource list
        found = False
        for row, resource in enumerate(self.resource_list):
            if resource["resourceID*"] == scanned_id:
                found = True
                resource['found'] = True
                if scanned_id not in self.scanned_resources:
                    # Mark cell background as green if not already scanned
                    self.grid.SetCellBackgroundColour(row, 0, wx.GREEN)
                    self.grid.SetCellBackgroundColour(row, 1, wx.GREEN)
                    self.grid.SetCellBackgroundColour(row, 2, wx.GREEN)
                    self.scanned_resources.add(scanned_id)  # Mark as scanned
                    self.grid.Refresh()  # Refresh to apply color
                else:
                    wx.MessageBox(f"Resource ID {scanned_id} has already been scanned.", "Duplicate Scan")
                break

        if not found:
            wx.MessageBox(f"Resource ID {scanned_id} is not in the list.", "Not Found")

    def on_import_csv(self, event):
        with wx.FileDialog(self, "Open CSV file", wildcard="CSV files (*.csv)|*.csv",
                           style=wx.FD_OPEN | wx.FD_FILE_MUST_EXIST) as fileDialog:
            if fileDialog.ShowModal() == wx.ID_CANCEL:
                return  # User cancelled the file dialog
            
            file_path = fileDialog.GetPath()
            self.load_resource_list_from_csv(file_path)

    def load_resource_list_from_csv(self, file_path):
        self.resource_list = []  # Clear existing data
        with open(file_path, mode='r', newline='', encoding='utf-8') as file:
            reader = csv.DictReader(file)
            for row in reader:
                self.resource_list.append({
                    'Title*': row['Title*'],
                    'Label Title': row['Label Title'],
                    'Qty': row['Qty'],
                    'speed': row['speed'],
                    'resdef*': row['resdef*'],
                    'Pri': row['Pri'],
                    'resourceID*': row['resourceID*'],
                    'res_link': row['res_link'],
                    'resdef_link': row['resdef_link'],
                    'location': row["location"],
                    'found': False
                })
        
        self.populate_grid()

    def on_paste_from_clipboard(self, event):
        if wx.TheClipboard.IsOpened() or wx.TheClipboard.Open():
            if wx.TheClipboard.IsSupported(wx.DataFormat(wx.DF_UNICODETEXT)):
                data = wx.TextDataObject()
                wx.TheClipboard.GetData(data)
                clipboard_text = data.GetText()
                self.load_resource_list_from_text(clipboard_text)
            wx.TheClipboard.Close()

    def load_resource_list_from_text(self, text):
        self.resource_list = []  # Clear existing data
        for line in text.strip().splitlines():
            line = line.strip()
            if line:
                row = line.split('\t')  # Assuming tab-separated values
                self.resource_list.append({
                    'resourceID*': row[0],
                    "res_link": '',
                    'Title*': '',
                    'found': False
                })
        self.populate_grid()

    def populate_grid(self):
        # Clear existing grid data
        self.grid.ClearGrid()
        if self.grid.GetNumberRows() > 0:
            self.grid.DeleteRows(0, self.grid.GetNumberRows())

        # Populate grid with new resource data
        self.grid.AppendRows(len(self.resource_list))
        for row, resource in enumerate(self.resource_list):
            if resource["resourceID*"]:
                self.grid.SetCellValue(row, 0, resource["resourceID*"])
            if resource["res_link"]:
                self.grid.SetCellValue(row, 1, resource['res_link'])
            if resource["Title*"]:
                self.grid.SetCellValue(row, 2, resource['Title*'])
            # Reset cell background color
            self.grid.SetCellBackgroundColour(row, 0, wx.WHITE)
            self.grid.SetCellBackgroundColour(row, 1, wx.WHITE)
            self.grid.SetCellBackgroundColour(row, 2, wx.WHITE)

        self.scanned_resources.clear()  # Reset scanned IDs
        self.grid.Refresh()  # Refresh grid to apply changes
    
    def show_count_report(self, event):
        if self.resource_list:
            for resource in self.resource_list:
                if not resource['found']:
                    print(resource)
