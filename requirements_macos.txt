# Resource Manager Python Dependencies
# Core GUI Framework
wxpython>=4.1.0

# HTTP Requests and API Communication
requests>=2.25.0
urllib3>=1.26.0

# Authentication (Kerberos for Apple internal APIs)
pykerberos>=1.2.1

# Data Processing and CSV Handling
# Note: pandas and numpy removed as we use custom CSVHandler

# Application Packaging
pyinstaller>=5.0.0

# Type Hints and Development
typing-extensions>=4.0.0

# Standard Library Dependencies (included with Python)
# - pathlib (Python 3.4+)
# - csv (built-in)
# - json (built-in)
# - re (built-in)
# - datetime (built-in)
# - logging (built-in)
# - threading (built-in)
# - queue (built-in)
# - os (built-in)
# - sys (built-in)
# - tempfile (built-in)
# - platform (built-in)
# - uuid (built-in)
# - collections (built-in)
# - abc (built-in)
# - enum (built-in)
# - dataclasses (Python 3.7+)
