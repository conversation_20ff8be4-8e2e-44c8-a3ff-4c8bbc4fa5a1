import sys
import os
import wx
import platform
import logging

# Set up logging with proper path for macOS app
import tempfile
log_dir = os.path.expanduser('~/Documents')
if not os.path.exists(log_dir):
    log_dir = tempfile.gettempdir()
log_file = os.path.join(log_dir, 'ResourceManager_app.log')

logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('main')

class ResourceManagerApp(wx.App):
    def __init__(self):
        logger.debug("Starting ResourceManagerApp initialization")
        
        # Set up project root and imports before wx initialization
        if getattr(sys, 'frozen', False):
            logger.debug("Running as frozen application")
            project_root = sys._MEIPASS
        else:
            logger.debug("Running in development mode")
            project_root = os.path.dirname(os.path.abspath(__file__))
        
        logger.debug(f"Project root: {project_root}")
        sys.path.append(project_root)
        
        # Import and initialize ResourceManager before wx
        logger.debug("Importing ResourceManager")
        from core.resource_manager import ResourceManager
        from core.resource_client import TSTTResourceClient

        # Create shared client instance
        resource_client = TSTTResourceClient()
        self.resource_manager = ResourceManager(resource_client)
        logger.debug("ResourceManager initialized with dependency injection")
        
        # Initialize wx App last
        logger.debug("Initializing wx.App")
        wx.App.__init__(self, False)

    def OnInit(self):
        try:
            logger.debug("Starting OnInit")
            
            # Import here to ensure wx is initialized first
            logger.debug("Importing MainFrame")
            from gui.main_frame import MainFrame
            
            # Set up macOS specific settings
            if platform.system() == 'Darwin':
                logger.debug("Configuring macOS specific settings")
                self.SetAppName("Resource Manager")
            
            logger.debug("Creating MainFrame")
            self.frame = MainFrame(None, self.resource_manager)
            
            logger.debug("Showing MainFrame")
            self.frame.Show()
            
            # Raise window to front on macOS
            if platform.system() == 'Darwin':
                logger.debug("Raising window to front")
                self.frame.Raise()
            
            logger.debug("OnInit completed successfully")
            return True
            
        except Exception as e:
            logger.error("Error in OnInit", exc_info=True)
            wx.MessageBox(f"Error initializing application: {str(e)}", "Initialization Error")
            return False

def main():
    try:
        logger.debug("Starting main()")
        app = ResourceManagerApp()
        logger.debug("ResourceManagerApp created, starting MainLoop")
        app.MainLoop()
        logger.debug("MainLoop completed")
    except Exception as e:
        logger.error("Fatal error in main()", exc_info=True)
        sys.exit(1)

if __name__ == "__main__":
    main()
