#!/usr/bin/env python3
"""
Setup script for Resource Manager Python application.
"""

from setuptools import setup, find_packages
import os
import sys

# Read the README file
def read_readme():
    with open("README_PYTHON.md", "r", encoding="utf-8") as fh:
        return fh.read()

# Read requirements
def read_requirements():
    with open("requirements_macos.txt", "r", encoding="utf-8") as fh:
        lines = fh.readlines()
        # Filter out comments and empty lines
        requirements = []
        for line in lines:
            line = line.strip()
            if line and not line.startswith('#'):
                requirements.append(line)
        return requirements

# Check Python version
if sys.version_info < (3, 7):
    print("Error: Resource Manager requires Python 3.7 or later.")
    sys.exit(1)

# Check macOS
if sys.platform != "darwin":
    print("Warning: Resource Manager is designed for macOS. Other platforms may not work correctly.")

setup(
    name="resource-manager-macos",
    version="1.0.0",
    author="Apple Inc.",
    author_email="<EMAIL>",
    description="macOS Resource Manager for Apple TSTT Resource API",
    long_description=read_readme(),
    long_description_content_type="text/markdown",
    url="https://internal.apple.com/resource-manager",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 5 - Production/Stable",
        "Intended Audience :: Developers",
        "License :: Other/Proprietary License",
        "Operating System :: MacOS",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.7",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Software Development :: Libraries :: Application Frameworks",
        "Topic :: System :: Systems Administration",
    ],
    python_requires=">=3.7",
    install_requires=read_requirements(),
    extras_require={
        "dev": [
            "pytest>=6.0",
            "pytest-cov>=2.0",
            "black>=21.0",
            "flake8>=3.8",
            "mypy>=0.800",
        ],
    },
    entry_points={
        "console_scripts": [
            "resource-manager=main:main",
        ],
        "gui_scripts": [
            "resource-manager-gui=main:main",
        ],
    },
    include_package_data=True,
    package_data={
        "": ["*.csv", "*.txt", "*.md"],
        "core": ["*.csv"],
        "templates": ["*.csv"],
        "assets": ["*"],
    },
    data_files=[
        ("", ["config.py", "requirements_macos.txt"]),
        ("templates", ["core/resource_update_template.csv"]),
    ],
    zip_safe=False,  # Required for PyInstaller compatibility
    keywords="apple macos resource management api tstt",
    project_urls={
        "Bug Reports": "https://internal.apple.com/resource-manager/issues",
        "Source": "https://internal.apple.com/resource-manager/source",
        "Documentation": "https://internal.apple.com/resource-manager/docs",
    },
)
