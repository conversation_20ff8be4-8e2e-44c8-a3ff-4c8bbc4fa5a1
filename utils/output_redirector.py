import wx
import queue

class OutputRedirector:
    def __init__(self, log_function):
        self.log_function = log_function
        self.message_queue = queue.Queue()  # Queue for storing messages from threads

    def write(self, message):
        self.message_queue.put(message)  # Put message in queue
        wx.CallAfter(self.log_function, message)  # Ensure UI update in the main thread

    def flush(self):
        pass
