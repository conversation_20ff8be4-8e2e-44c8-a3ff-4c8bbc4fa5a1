"""
Data Access Layer for Resource Manager application.

This module provides a unified interface for all data access operations,
including API calls, file I/O, and data persistence.
"""

import csv
from pathlib import Path
from typing import Dict, List, Optional, Any, Union



from core.interfaces import ResourceClientInterface, FileHandlerInterface
from utils.file_path_manager import file_path_manager
from config import config


class <PERSON><PERSON><PERSON>ler(FileHandlerInterface):
    """Implementation of file handling operations."""
    
    def read_csv(self, filepath: Union[str, Path]) -> List[List[str]]:
        """Read CSV file and return data as list of lists."""
        file_path = file_path_manager.get_csv_file_path(str(filepath))
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as csv_file:
            reader = csv.reader(csv_file)
            return list(reader)
    
    def write_csv(self, header: List[str], data: List[List[str]], filepath: Union[str, Path]) -> None:
        """Write data to CSV file."""
        file_path = file_path_manager.get_csv_file_path(str(filepath))
        with open(file_path, 'w', newline='') as csv_file:
            writer = csv.writer(csv_file)
            writer.writerow(header)
            writer.writerows(data)
    
    def file_exists(self, filepath: Union[str, Path]) -> bool:
        """Check if file exists."""
        return file_path_manager.file_exists(filepath)
    
    def remove_file_if_exists(self, filepath: Union[str, Path]) -> bool:
        """Remove file if it exists."""
        return file_path_manager.remove_file_if_exists(filepath)
    
    def get_timestamped_filename(self, base_name: str, extension: str = "csv") -> str:
        """Generate timestamped filename."""
        return file_path_manager.get_timestamped_filename(base_name, extension)


class DataAccessLayer:
    """
    Unified data access layer that coordinates between different data sources.
    
    This class provides a single point of access for all data operations,
    abstracting away the details of whether data comes from APIs, files, or other sources.
    """
    
    def __init__(self, resource_client: ResourceClientInterface, file_handler: FileHandlerInterface = None):
        """
        Initialize DataAccessLayer with required dependencies.
        
        Args:
            resource_client: Client for API operations
            file_handler: Handler for file operations (optional, creates default if None)
        """
        self.resource_client = resource_client
        self.file_handler = file_handler or FileHandler()
    
    # API Operations
    def find_resource_definitions(self, criteria: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Find resource definitions based on criteria."""
        return self.resource_client.find_resource_definitions(criteria)
    
    def get_resource_definition_by_id(self, resdef_id: int) -> Dict[str, Any]:
        """Get resource definition by ID."""
        return self.resource_client.resource_definition_for_id(resdef_id)
    
    def get_resource_by_id(self, resource_id: int) -> Dict[str, Any]:
        """Get resource by ID."""
        return self.resource_client.resource_for_id(resource_id)
    
    def get_resource_fields(self, resource_id: int, fields: List[str]) -> Dict[str, Any]:
        """Get specific fields for a resource."""
        return self.resource_client.fields_for_resource_id(resource_id, request_fields=fields)
    
    def get_resource_definition_fields(self, resdef_id: int, fields: List[str]) -> Dict[str, Any]:
        """Get specific fields for a resource definition."""
        return self.resource_client.fields_for_resource_definition_id(resdef_id, request_fields=fields)
    
    def create_resource(self, resource_data: Dict[str, Any]) -> Any:
        """Create a new resource."""
        return self.resource_client.create_resource(resource_data)
    
    def update_resource(self, resource_id: int, resource_data: Dict[str, Any]) -> Any:
        """Update an existing resource."""
        return self.resource_client.update_resource(resource_id, resource_data)
    
    # File Operations
    def read_csv_data(self, filename: str) -> List[List[str]]:
        """Read CSV data from file."""
        return self.file_handler.read_csv(filename)
    
    def write_csv_data(self, header: List[str], data: List[List[str]], filename: str) -> None:
        """Write CSV data to file."""
        self.file_handler.write_csv(header, data, filename)
    
    def read_pandas_csv(self, filepath: Union[str, Path]) -> pd.DataFrame:
        """Read CSV file using pandas."""
        if isinstance(filepath, str):
            file_path = file_path_manager.get_csv_file_path(filepath)
        else:
            file_path = filepath
        return CSVHandler.read_csv(file_path)
    
    def write_pandas_csv(self, df: pd.DataFrame, filepath: Union[str, Path], **kwargs) -> None:
        """Write DataFrame to CSV file."""
        if isinstance(filepath, str):
            file_path = file_path_manager.get_output_file_path(filepath)
        else:
            file_path = filepath
        CSVHandler.write_csv(data, file_path, index=False, **kwargs)
    
    # Data Processing Operations
    def process_print_data(self, filename: str = 'resource_dup.csv') -> str:
        """
        Process data for printing and return output file path.
        
        Args:
            filename: Input CSV filename
            
        Returns:
            Path to the generated output file
        """
        # Read the CSV file
        df = self.read_pandas_csv(filename)
        
        # Add resdef_link if resdef* exists and is not null
        if not df['resdef*'].isnull().values.any():
            df["resdef_link"] = "rdar://resdef/" + df["resdef*"].astype(str)
        
        # Add res_link
        df["res_link"] = "rdar://res/" + df["resourceID*"].astype(str)
        
        # Convert Priority
        df["Priority"] = np.where(df["Pri"] == config.PRIORITY_NA_VALUE, 'N/A', df["Pri"] - 1)
        
        # If there's only one row (excluding header), duplicate it
        if len(df) == 1:
            df = pd.concat([df, df], ignore_index=True)
        
        # Generate output file path
        output_file = file_path_manager.get_label_print_output_path()
        
        # Save to CSV
        self.write_pandas_csv(df, output_file)
        
        return str(output_file)
    
    def clean_duplicate_file(self, filename: str = 'resource_dup.csv') -> None:
        """Remove duplicate file if it exists."""
        if filename == 'resource_dup.csv':
            file_path = file_path_manager.get_resource_dup_csv_path()
        else:
            file_path = file_path_manager.get_csv_file_path(filename)
        
        file_path_manager.remove_file_if_exists(file_path)
    
    # Validation Operations
    def validate_csv_structure(self, filename: str, required_columns: List[str]) -> bool:
        """
        Validate that CSV file has required columns.
        
        Args:
            filename: CSV filename to validate
            required_columns: List of required column names
            
        Returns:
            True if valid, False otherwise
        """
        try:
            df = self.read_pandas_csv(filename)
            return all(col in df.columns for col in required_columns)
        except Exception:
            return False
    
    def validate_resource_data(self, resource_data: Dict[str, Any]) -> bool:
        """
        Validate resource data structure.
        
        Args:
            resource_data: Resource data to validate
            
        Returns:
            True if valid, False otherwise
        """
        required_fields = ['title', 'componentId', 'classId', 'stateId', 'categoryId', 'priority']
        return all(field in resource_data for field in required_fields)
    
    # Backup Operations
    def create_backup(self, filename: str) -> Optional[str]:
        """
        Create a backup of the specified file.
        
        Args:
            filename: File to backup
            
        Returns:
            Path to backup file if successful, None otherwise
        """
        try:
            original_path = file_path_manager.get_csv_file_path(filename)
            if file_path_manager.file_exists(original_path):
                backup_path = file_path_manager.create_backup_path(original_path)
                # Copy file content
                with open(original_path, 'r') as src, open(backup_path, 'w') as dst:
                    dst.write(src.read())
                return str(backup_path)
        except Exception:
            pass
        return None
    
    # Utility Operations
    def get_file_info(self, filename: str) -> Dict[str, Any]:
        """
        Get information about a file.
        
        Args:
            filename: File to get info for
            
        Returns:
            Dictionary with file information
        """
        file_path = file_path_manager.get_csv_file_path(filename)
        if file_path_manager.file_exists(file_path):
            stat = file_path.stat()
            return {
                'exists': True,
                'size': stat.st_size,
                'modified': stat.st_mtime,
                'path': str(file_path)
            }
        return {'exists': False}


# Global instance
def create_data_access_layer(resource_client: ResourceClientInterface) -> DataAccessLayer:
    """Factory function to create DataAccessLayer instance."""
    return DataAccessLayer(resource_client)
