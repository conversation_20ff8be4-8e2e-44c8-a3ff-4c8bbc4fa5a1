# Resource Manager Python Package

## 📦 Package Overview

This is the complete Python source code package for the Resource Manager application, designed for Apple internal resource management through the TSTT Resource API.

**Package File**: `resource_manager_python.zip` (71KB)
**SHA256**: `d0ee48677af3794148ec925d01700e1744fcc5a1d4ba3fde46d870b02eeefaec`

## 📋 Package Contents

```
resource_manager_python/
├── main.py                          # Application entry point
├── config.py                        # Configuration settings
├── requirements_macos.txt           # Python dependencies
├── setup.py                         # Package setup script
├── MANIFEST.in                      # Package manifest
├── INSTALLATION.txt                 # Installation instructions
├── PACKAGE_INFO.txt                 # Package information
├── core/                            # Core business logic (13 modules)
│   ├── Resource.py                  # Main resource operations
│   ├── resource_manager.py          # Resource management service
│   ├── resource_client.py           # API client
│   ├── application_service.py       # Application service layer
│   ├── data_access_layer.py         # Data access abstraction
│   ├── interfaces.py                # Interface definitions
│   ├── exceptions.py                # Custom exceptions
│   ├── response_models.py           # Response data models
│   └── ...                          # Additional core modules
├── gui/                             # User interface (5 modules)
│   ├── main_frame.py               # Main application window
│   └── panels/                     # UI panels
│       ├── create_panel.py         # Resource creation
│       ├── update_panel.py         # Resource updates
│       ├── print_panel.py          # Print/export
│       └── resource_count_panel.py # Statistics
├── utils/                           # Utilities (3 modules)
│   ├── csv_handler.py              # CSV processing
│   ├── file_path_manager.py        # File operations
│   └── output_redirector.py        # GUI output
├── templates/                       # CSV templates
│   ├── resource_create.csv
│   ├── resource_update_template.csv
│   └── resource_dup.csv
├── documentation/                   # Complete documentation
│   ├── README_PYTHON.md            # User guide (300 lines)
│   ├── DEVELOPMENT.md              # Developer guide (300 lines)
│   └── CHANGELOG.md                # Version history
├── scripts/                         # Installation utilities
│   └── install.sh                  # Automated installation
└── assets/                         # Application assets
    ├── icons/
    └── images/
```

## 🔧 Dependencies

### Core Dependencies
```
wxpython>=4.1.0          # GUI framework
requests>=2.25.0         # HTTP client
urllib3>=1.26.0          # HTTP utilities
pykerberos>=1.2.1        # Kerberos authentication
pyinstaller>=5.0.0       # Application packaging
typing-extensions>=4.0.0 # Type hints
```

### Standard Library (Built-in)
- File Operations: pathlib, csv, os, tempfile
- Data Processing: json, re, datetime, collections
- Concurrency: threading, queue
- System: sys, platform, uuid, logging
- Architecture: abc, enum, dataclasses

## 🚀 Quick Start

### 1. Extract Package
```bash
unzip resource_manager_python.zip
cd resource_manager_python
```

### 2. Automated Installation (Recommended)
```bash
./scripts/install.sh
```

### 3. Manual Installation
```bash
# Create virtual environment
python3 -m venv venv
source venv/bin/activate

# Install dependencies
pip install -r requirements_macos.txt

# Run application
python main.py
```

### 4. Authentication Setup
```bash
# Setup Kerberos (required for Apple APIs)
kinit <EMAIL>
```

## ✨ Features

### Resource Management
- **Create Resources**: Single or batch creation from CSV files
- **Update Resources**: Modify existing resources with new data
- **Print Resources**: Generate CSV reports with clickable hyperlinks
- **Resource Count**: View statistics and tracking information

### Technical Features
- **Native GUI**: wxPython-based macOS application
- **API Integration**: Direct TSTT Resource API communication
- **CSV Processing**: Custom lightweight CSV handler (no pandas dependency)
- **Error Handling**: Comprehensive error management and user feedback
- **Debug Logging**: Configurable debug output for troubleshooting
- **File Management**: Centralized path and file operations

## 🏗️ Architecture

### Clean Architecture Pattern
- **Presentation Layer**: GUI components (`gui/`)
- **Application Layer**: Service coordination (`core/application_service.py`)
- **Domain Layer**: Business logic (`core/resource_manager.py`, `core/Resource.py`)
- **Infrastructure Layer**: API clients and data access (`core/resource_client.py`)

### Key Design Principles
- **Dependency Injection**: Clean separation of concerns
- **Interface-Based Design**: Testable and maintainable code
- **Observer Pattern**: Event-driven GUI updates
- **Error Handling**: Comprehensive exception hierarchy
- **Type Safety**: Type hints throughout codebase

## 📖 Documentation

### User Documentation
- **README_PYTHON.md**: Complete user guide with installation, usage, and troubleshooting
- **INSTALLATION.txt**: Quick start installation instructions

### Developer Documentation
- **DEVELOPMENT.md**: Architecture overview, development setup, and contribution guidelines
- **Code Comments**: Comprehensive inline documentation
- **Type Hints**: Full type annotation for better IDE support

## 🔐 System Requirements

- **macOS**: 10.14 (Mojave) or later
- **Python**: 3.7 or later
- **Network**: Access to Apple internal networks
- **Authentication**: Valid Apple Connect credentials
- **Permissions**: File system access for CSV operations

## 🐛 Troubleshooting

### Common Issues
1. **Authentication Errors**: Run `kinit <EMAIL>`
2. **GUI Issues**: Ensure wxPython is properly installed
3. **API Errors**: Check network connectivity and VPN
4. **File Permissions**: Grant file system access when prompted

### Debug Mode
```bash
DEBUG_LOGGING=1 python main.py
```

### Log Files
Application logs: `~/Documents/ResourceManager_app.log`

## 📊 Package Statistics

- **Total Files**: 54 files
- **Source Code**: 21 Python modules
- **Documentation**: 3 comprehensive guides
- **Templates**: 3 CSV templates
- **Package Size**: 71KB (compressed)
- **Uncompressed Size**: ~255KB

## 🔄 Version Information

- **Version**: 1.0.0
- **Build Date**: 2024-06-23
- **Python Compatibility**: 3.7+
- **Platform**: macOS (Universal)

## 📞 Support

For internal Apple support:
1. Check the troubleshooting section in documentation
2. Review application logs for error details
3. Enable debug mode for detailed output
4. Contact the development team for assistance

---

**Status**: ✅ Ready for Distribution
**Package Type**: Pure Python Source Code
**Target Platform**: macOS with Apple Internal Network Access
