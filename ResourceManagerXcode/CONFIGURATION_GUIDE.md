# Resource Manager Configuration Guide

## Overview

The Swift version of Resource Manager now includes a configuration system that allows users to customize default values for resource creation and update operations. This eliminates the need to hardcode values in the source code.

## Accessing Configuration

1. Launch the Resource Manager application
2. In the menu bar, click **Configuration** → **Default Values...**
3. The configuration window will open with all configurable fields

## Configurable Fields

The following fields can be customized:

### Resource Creation Fields
- **Component ID**: Default component identifier for new resources (default: 1118899)
- **DRI ID**: Default DRI (Directly Responsible Individual) identifier (default: 973776146)
- **Inventory Keeper ID**: Default inventory keeper identifier (default: 973776146)
- **Location ID**: Default location identifier (default: 66842)
- **Specific Location**: Default specific location name (default: "Aruba")
- **Class ID**: Default class identifier (default: 1)
- **State ID**: Default state identifier (default: 1)

## Configuration Operations

### Saving Configuration
1. Modify the desired field values in the configuration window
2. Click the **Save** button
3. A confirmation dialog will appear
4. The new values will be used for all subsequent resource operations

### Resetting to Defaults
1. In the configuration window, click the **Reset** button
2. A confirmation dialog will appear asking if you want to reset all values
3. Click **Reset** to confirm, or **Cancel** to abort
4. All fields will be restored to their original default values
5. Click **Save** to apply the reset values

### Canceling Changes
- Click the **Cancel** button to close the configuration window without saving changes

## How Configuration Affects Operations

### Resource Creation
When creating new resources (single or batch), the system will use the configured default values for:
- Component ID, Class ID, State ID, Location ID, DRI ID, Inventory Keeper ID
- Specific Location (when no location is provided in the UI)

### Resource Updates
When updating resources, the system will use:
- Specific Location from configuration as the default location value

## Persistence

Configuration values are automatically saved to the system's UserDefaults and will persist between application launches. Your customized values will be remembered and used until you change them again.

## Technical Notes

- Configuration values are validated when entered (must be valid integers for ID fields)
- Invalid values will not be saved
- The configuration system uses UserDefaults for storage
- Changes take effect immediately after saving
- No application restart is required

## Troubleshooting

If you encounter issues with configuration:

1. **Values not saving**: Ensure all ID fields contain valid integer values
2. **Reset not working**: Try manually entering the default values and saving
3. **Configuration window not opening**: Check the Configuration menu in the menu bar

## Default Values Reference

For reference, here are the original hardcoded default values:

```
Component ID: 1118899
DRI ID: 973776146
Inventory Keeper ID: 973776146
Location ID: 66842
Specific Location: "Aruba"
Class ID: 1
State ID: 1
```

These values can be restored at any time using the Reset button in the configuration window.

## QR Code Feature

### Overview
The Resource Manager now includes QR code generation in the preview table. Each resource's link is automatically converted to a QR code for easy scanning and sharing.

### QR Code Column
- **Location**: The QR code appears in the last column of the preview table
- **Content**: Each QR code contains the resource link (e.g., rdar://res/284158)
- **Size**: QR codes are automatically sized to fit within the table cell
- **Quality**: High-resolution QR codes suitable for scanning

### Usage
1. Create or update resources as usual
2. Open the preview window after resource operations
3. The QR code column will display QR codes for each resource link
4. QR codes can be scanned with any QR code reader to open the resource link
5. QR codes are read-only and cannot be edited in the table

### Technical Details
- QR codes are generated using Core Image's CIQRCodeGenerator
- Fallback to system QR code symbol if generation fails
- QR codes are generated in real-time when the preview window opens
- No additional dependencies required - uses built-in macOS frameworks

### Benefits
- **Quick Access**: Scan QR codes to quickly open resource links on mobile devices
- **Sharing**: Easy to share resource links by showing QR codes
- **Documentation**: Include QR codes in printed reports for easy digital access
- **Cross-Platform**: QR codes work with any QR scanner on any device

## Preview Table Improvements

### Enhanced Row Height
The preview table now features increased row height (2x the original size) for better readability and visual comfort:

- **Original Height**: 24 pixels
- **New Height**: 48 pixels (100% increase)
- **Implementation**:
  - Set via both `tableView.rowHeight` property and `heightOfRow` delegate method
  - Forced layout updates ensure changes take effect immediately
  - Increased intercell spacing for better visual separation
- **Benefits**:
  - Better text readability, especially for longer resource titles
  - More comfortable viewing experience
  - Better accommodation for QR codes in the last column
  - Improved overall visual hierarchy
  - Easier cell selection and interaction

### Visual Enhancements
- **Alternating Row Colors**: Enabled for better row distinction
- **Proper Cell Spacing**: Optimized intercell spacing for cleaner appearance
- **Responsive Layout**: Table automatically adjusts to window size changes
- **Column Resizing**: Users can resize columns as needed for optimal viewing
