<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.Cocoa.XIB" version="3.0" toolsVersion="24105" targetRuntime="MacOSX.Cocoa" propertyAccessControl="none" useAutolayout="YES" customObjectInstantitationMethod="direct">
    <dependencies>
        <deployment identifier="macosx"/>
        <plugIn identifier="com.apple.InterfaceBuilder.CocoaPlugin" version="24105"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <customObject id="-2" userLabel="File's Owner" customClass="ResourcePreviewWindowController" customModule="ResourceManager" customModuleProvider="target">
            <connections>
                <outlet property="tableView" destination="4" id="10"/>
                <outlet property="titleLabel" destination="3" id="9"/>
                <outlet property="window" destination="1" id="13"/>
            </connections>
        </customObject>
        <customObject id="-1" userLabel="First Responder" customClass="FirstResponder"/>
        <customObject id="-3" userLabel="Application" customClass="NSObject"/>
        <window title="Resource Print Preview" allowsToolTipsWhenApplicationIsInactive="NO" autorecalculatesKeyViewLoop="NO" releasedWhenClosed="NO" animationBehavior="default" id="1">
            <windowStyleMask key="styleMask" titled="YES" closable="YES" miniaturizable="YES" resizable="YES"/>
            <windowPositionMask key="initialPositionMask" leftStrut="YES" rightStrut="YES" topStrut="YES" bottomStrut="YES"/>
            <rect key="contentRect" x="196" y="240" width="1200" height="600"/>
            <rect key="screenRect" x="0.0" y="0.0" width="1512" height="948"/>
            <view key="contentView" wantsLayer="YES" id="2">
                <rect key="frame" x="0.0" y="0.0" width="1200" height="600"/>
                <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                <subviews>
                    <textField focusRingType="none" horizontalHuggingPriority="251" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="3">
                        <rect key="frame" x="18" y="561" width="1164" height="19"/>
                        <textFieldCell key="cell" lineBreakMode="clipping" title="Preview of resources to be printed" id="14">
                            <font key="font" metaFont="system" size="16"/>
                            <color key="textColor" name="labelColor" catalog="System" colorSpace="catalog"/>
                            <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                        </textFieldCell>
                    </textField>
                    <scrollView autohidesScrollers="YES" horizontalLineScroll="26" horizontalPageScroll="10" verticalLineScroll="26" verticalPageScroll="10" usesPredominantAxisScrolling="NO" translatesAutoresizingMaskIntoConstraints="NO" id="5">
                        <rect key="frame" x="20" y="20" width="1160" height="521"/>
                        <clipView key="contentView" id="6">
                            <rect key="frame" x="1" y="1" width="1158" height="519"/>
                            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                            <subviews>
                                <tableView verticalHuggingPriority="750" allowsExpansionToolTips="YES" columnAutoresizingStyle="lastColumnOnly" columnSelection="YES" multipleSelection="NO" autosaveColumns="NO" rowHeight="24" rowSizeStyle="medium" headerView="15" id="4">
                                    <rect key="frame" x="0.0" y="0.0" width="1158" height="496"/>
                                    <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                    <size key="intercellSpacing" width="3" height="2"/>
                                    <color key="backgroundColor" name="controlBackgroundColor" catalog="System" colorSpace="catalog"/>
                                    <color key="gridColor" name="gridColor" catalog="System" colorSpace="catalog"/>
                                </tableView>
                            </subviews>
                        </clipView>
                        <scroller key="horizontalScroller" hidden="YES" wantsLayer="YES" verticalHuggingPriority="750" horizontal="YES" id="16">
                            <rect key="frame" x="1" y="463" width="1158" height="16"/>
                            <autoresizingMask key="autoresizingMask"/>
                        </scroller>
                        <scroller key="verticalScroller" hidden="YES" wantsLayer="YES" verticalHuggingPriority="750" horizontal="NO" id="17">
                            <rect key="frame" x="1144" y="17" width="15" height="102"/>
                            <autoresizingMask key="autoresizingMask"/>
                        </scroller>
                        <tableHeaderView key="headerView" id="15">
                            <rect key="frame" x="0.0" y="0.0" width="1158" height="23"/>
                            <autoresizingMask key="autoresizingMask"/>
                        </tableHeaderView>
                    </scrollView>
                </subviews>
                <constraints>
                    <constraint firstItem="3" firstAttribute="top" secondItem="2" secondAttribute="top" constant="20" id="22"/>
                    <constraint firstItem="3" firstAttribute="leading" secondItem="2" secondAttribute="leading" constant="20" id="23"/>
                    <constraint firstAttribute="trailing" secondItem="3" secondAttribute="trailing" constant="20" id="24"/>
                    <constraint firstItem="5" firstAttribute="top" secondItem="3" secondAttribute="bottom" constant="20" id="25"/>
                    <constraint firstItem="5" firstAttribute="leading" secondItem="2" secondAttribute="leading" constant="20" id="26"/>
                    <constraint firstAttribute="trailing" secondItem="5" secondAttribute="trailing" constant="20" id="27"/>
                    <constraint firstAttribute="bottom" secondItem="5" secondAttribute="bottom" constant="20" id="33"/>
                </constraints>
            </view>
            <point key="canvasLocation" x="139" y="154"/>
        </window>
    </objects>
</document>
