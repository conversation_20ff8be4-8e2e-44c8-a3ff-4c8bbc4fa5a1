<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.Cocoa.Storyboard.XIB" version="3.0" toolsVersion="24105" targetRuntime="MacOSX.Cocoa" propertyAccessControl="none" useAutolayout="YES" initialViewController="B8D-0N-5wS">
    <dependencies>
        <deployment identifier="macosx"/>
        <plugIn identifier="com.apple.InterfaceBuilder.CocoaPlugin" version="24105"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <scenes>
        <!--Application-->
        <scene sceneID="JPo-4y-FX3">
            <objects>
                <application id="hnw-xV-0zn" sceneMemberID="viewController">
                    <menu key="mainMenu" title="Main Menu" systemMenu="main" id="AYu-sK-qS6">
                        <items>
                            <menuItem title="ResourceManager" id="1Xt-HY-uBw">
                                <modifierMask key="keyEquivalentModifierMask"/>
                                <menu key="submenu" title="ResourceManager" systemMenu="apple" id="uQy-DD-JDr">
                                    <items>
                                        <menuItem title="About ResourceManager" id="5kV-Vb-QxS">
                                            <modifierMask key="keyEquivalentModifierMask"/>
                                            <connections>
                                                <action selector="orderFrontStandardAboutPanel:" target="Ady-hI-5gd" id="Exp-CZ-Vem"/>
                                            </connections>
                                        </menuItem>
                                        <menuItem isSeparatorItem="YES" id="VOq-y0-SEH"/>
                                        <menuItem title="Quit ResourceManager" keyEquivalent="q" id="4sb-4s-VLi">
                                            <connections>
                                                <action selector="terminate:" target="Ady-hI-5gd" id="Te7-pn-YzF"/>
                                            </connections>
                                        </menuItem>
                                    </items>
                                </menu>
                            </menuItem>
                            <menuItem title="File" id="dMs-cI-mzQ">
                                <modifierMask key="keyEquivalentModifierMask"/>
                                <menu key="submenu" title="File" id="bib-Uj-vzu">
                                    <items>
                                        <menuItem title="New Resource" keyEquivalent="n" id="Was-JA-tGl">
                                            <connections>
                                                <action selector="createResourceClicked:" target="XfG-lQ-9wD" id="gN8-uF-Gmf"/>
                                            </connections>
                                        </menuItem>
                                        <menuItem title="Open CSV..." keyEquivalent="o" id="IAo-SY-fd9">
                                            <connections>
                                                <action selector="csvButtonClicked:" target="XfG-lQ-9wD" id="4ZA-gO-b42"/>
                                            </connections>
                                        </menuItem>
                                        <menuItem isSeparatorItem="YES" id="m54-Is-iLE"/>
                                    </items>
                                </menu>
                            </menuItem>
                        </items>
                    </menu>
                    <connections>
                        <outlet property="delegate" destination="Voe-Tx-rLC" id="PrD-fu-P6m"/>
                    </connections>
                </application>
                <customObject id="Voe-Tx-rLC" customClass="AppDelegate" customModule="ResourceManager" customModuleProvider="target"/>
                <customObject id="Ady-hI-5gd" userLabel="First Responder" customClass="NSResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="75" y="0.0"/>
        </scene>
        <!--Window Controller-->
        <scene sceneID="R2V-B0-nI4">
            <objects>
                <windowController id="B8D-0N-5wS" sceneMemberID="viewController">
                    <window key="window" title="Resource Manager" allowsToolTipsWhenApplicationIsInactive="NO" autorecalculatesKeyViewLoop="NO" releasedWhenClosed="NO" animationBehavior="default" id="IQv-IB-iLA">
                        <windowStyleMask key="styleMask" titled="YES" closable="YES" miniaturizable="YES" resizable="YES"/>
                        <windowPositionMask key="initialPositionMask" leftStrut="YES" rightStrut="YES" topStrut="YES" bottomStrut="YES"/>
                        <rect key="contentRect" x="196" y="240" width="800" height="600"/>
                        <rect key="screenRect" x="0.0" y="0.0" width="1680" height="1027"/>
                    </window>
                    <connections>
                        <segue destination="XfG-lQ-9wD" kind="relationship" relationship="window.shadowedContentViewController" id="cq2-FE-JQM"/>
                    </connections>
                </windowController>
                <customObject id="Ytg-gk-tKL" userLabel="First Responder" customClass="NSResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="75" y="250"/>
        </scene>
        <!--View Controller-->
        <scene sceneID="hIz-AP-VOD">
            <objects>
                <viewController id="XfG-lQ-9wD" customClass="ViewController" customModule="ResourceManager" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" wantsLayer="YES" id="m2S-Jp-Qdl">
                        <rect key="frame" x="0.0" y="0.0" width="800" height="600"/>
                        <autoresizingMask key="autoresizingMask"/>
                        <subviews>
                            <tabView fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="dkI-za-hwg">
                                <rect key="frame" x="20" y="20" width="760" height="560"/>
                                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                <font key="font" metaFont="system"/>
                                <tabViewItems>
                                    <tabViewItem label="Create Resource" identifier="create" id="Tbd-mf-2hc">
                                        <view key="view" id="Hz6-so-Iqo">
                                            <rect key="frame" x="10" y="33" width="740" height="514"/>
                                            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                            <subviews>
                                                <textField focusRingType="none" horizontalHuggingPriority="251" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="Nkf-gd-Yzc">
                                                    <rect key="frame" x="18" y="474" width="704" height="22"/>
                                                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                    <textFieldCell key="cell" lineBreakMode="clipping" title="Create a New Resource (* indicates required field)" id="Xhg-Wd-Yzc">
                                                        <font key="font" metaFont="systemBold" size="16"/>
                                                        <color key="textColor" name="labelColor" catalog="System" colorSpace="catalog"/>
                                                        <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                    </textFieldCell>
                                                </textField>
                                                <textField focusRingType="none" horizontalHuggingPriority="251" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="Yzc-gd-Nkf">
                                                    <rect key="frame" x="18" y="444" width="50" height="17"/>
                                                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                    <textFieldCell key="cell" lineBreakMode="clipping" title="Title*:" id="gd-Nkf-Yzc">
                                                        <font key="font" metaFont="system"/>
                                                        <color key="textColor" name="labelColor" catalog="System" colorSpace="catalog"/>
                                                        <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                    </textFieldCell>
                                                </textField>
                                                <textField focusRingType="none" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="abc-def-ghi">
                                                    <rect key="frame" x="80" y="442" width="300" height="22"/>
                                                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                    <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" selectable="YES" editable="YES" sendsActionOnEndEditing="YES" borderStyle="bezel" drawsBackground="YES" id="def-ghi-abc">
                                                        <font key="font" metaFont="system"/>
                                                        <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                        <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                    </textFieldCell>
                                                </textField>
                                                <textField focusRingType="none" horizontalHuggingPriority="251" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="jkl-mno-pqr">
                                                    <rect key="frame" x="400" y="444" width="70" height="17"/>
                                                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                    <textFieldCell key="cell" lineBreakMode="clipping" title="ResdefID*:" id="mno-pqr-jkl">
                                                        <font key="font" metaFont="system"/>
                                                        <color key="textColor" name="labelColor" catalog="System" colorSpace="catalog"/>
                                                        <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                    </textFieldCell>
                                                </textField>
                                                <textField focusRingType="none" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="stu-vwx-yz1">
                                                    <rect key="frame" x="480" y="442" width="120" height="22"/>
                                                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                    <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" selectable="YES" editable="YES" sendsActionOnEndEditing="YES" borderStyle="bezel" drawsBackground="YES" id="vwx-yz1-stu">
                                                        <font key="font" metaFont="system"/>
                                                        <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                        <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                    </textFieldCell>
                                                </textField>

                                                <textField focusRingType="none" horizontalHuggingPriority="251" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="Qty-Lab-el1">
                                                    <rect key="frame" x="18" y="414" width="60" height="17"/>
                                                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                    <textFieldCell key="cell" lineBreakMode="clipping" title="Quantity:" id="Qty-Lab-el2">
                                                        <font key="font" metaFont="system"/>
                                                        <color key="textColor" name="labelColor" catalog="System" colorSpace="catalog"/>
                                                        <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                    </textFieldCell>
                                                </textField>
                                                <textField focusRingType="none" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="Qty-Tex-tF1">
                                                    <rect key="frame" x="88" y="412" width="60" height="22"/>
                                                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                    <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" selectable="YES" editable="YES" sendsActionOnEndEditing="YES" borderStyle="bezel" drawsBackground="YES" id="Qty-Tex-tC1">
                                                        <font key="font" metaFont="system"/>
                                                        <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                        <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                    </textFieldCell>
                                                </textField>
                                                <textField focusRingType="none" horizontalHuggingPriority="251" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="Loc-Lab-el1">
                                                    <rect key="frame" x="170" y="414" width="60" height="17"/>
                                                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                    <textFieldCell key="cell" lineBreakMode="clipping" title="Location:" id="Loc-Lab-el2">
                                                        <font key="font" metaFont="system"/>
                                                        <color key="textColor" name="labelColor" catalog="System" colorSpace="catalog"/>
                                                        <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                    </textFieldCell>
                                                </textField>
                                                <textField focusRingType="none" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="Loc-Tex-tF1">
                                                    <rect key="frame" x="240" y="412" width="100" height="22"/>
                                                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                    <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" selectable="YES" editable="YES" sendsActionOnEndEditing="YES" borderStyle="bezel" drawsBackground="YES" id="Loc-Tex-tC1">
                                                        <font key="font" metaFont="system"/>
                                                        <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                        <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                    </textFieldCell>
                                                </textField>
                                                <textField focusRingType="none" horizontalHuggingPriority="251" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="Cat-Lab-el1">
                                                    <rect key="frame" x="360" y="414" width="80" height="17"/>
                                                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                    <textFieldCell key="cell" lineBreakMode="clipping" title="Category ID:" id="Cat-Lab-el2">
                                                        <font key="font" metaFont="system"/>
                                                        <color key="textColor" name="labelColor" catalog="System" colorSpace="catalog"/>
                                                        <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                    </textFieldCell>
                                                </textField>
                                                <textField focusRingType="none" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="Cat-Tex-tF1">
                                                    <rect key="frame" x="450" y="412" width="100" height="22"/>
                                                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                    <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" selectable="YES" editable="YES" sendsActionOnEndEditing="YES" borderStyle="bezel" drawsBackground="YES" id="Cat-Tex-tC1">
                                                        <font key="font" metaFont="system"/>
                                                        <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                        <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                    </textFieldCell>
                                                </textField>
                                                <button verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="234-567-890">
                                                    <rect key="frame" x="20" y="370" width="120" height="32"/>
                                                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                    <buttonCell key="cell" type="push" title="Create Resource" bezelStyle="rounded" alignment="center" borderStyle="border" imageScaling="proportionallyDown" inset="2" id="567-890-234">
                                                        <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                                                        <font key="font" metaFont="system"/>
                                                    </buttonCell>
                                                    <connections>
                                                        <action selector="createResourceClicked:" target="XfG-lQ-9wD" id="890-234-567"/>
                                                    </connections>
                                                </button>
                                                <button verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="345-678-901">
                                                    <rect key="frame" x="160" y="370" width="150" height="32"/>
                                                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                    <buttonCell key="cell" type="push" title="Create from CSV File" bezelStyle="rounded" alignment="center" borderStyle="border" imageScaling="proportionallyDown" inset="2" id="678-901-345">
                                                        <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                                                        <font key="font" metaFont="system"/>
                                                    </buttonCell>
                                                    <connections>
                                                        <action selector="csvButtonClicked:" target="XfG-lQ-9wD" id="901-345-678"/>
                                                    </connections>
                                                </button>
                                                <button verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="Prt-Res-Btn1">
                                                    <rect key="frame" x="330" y="370" width="120" height="32"/>
                                                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                    <buttonCell key="cell" type="push" title="Print Resources" bezelStyle="rounded" alignment="center" borderStyle="border" imageScaling="proportionallyDown" inset="2" id="Prt-Res-Cel1">
                                                        <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                                                        <font key="font" metaFont="system"/>
                                                    </buttonCell>
                                                    <connections>
                                                        <action selector="printResourcesClicked:" target="XfG-lQ-9wD" id="Prt-Res-Act1"/>
                                                    </connections>
                                                </button>
                                                <scrollView fixedFrame="YES" autohidesScrollers="YES" horizontalLineScroll="10" horizontalPageScroll="10" verticalLineScroll="10" verticalPageScroll="10" usesPredominantAxisScrolling="NO" translatesAutoresizingMaskIntoConstraints="NO" id="***********">
                                                    <rect key="frame" x="20" y="20" width="700" height="330"/>
                                                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                    <clipView key="contentView" id="***********">
                                                        <rect key="frame" x="1" y="1" width="698" height="328"/>
                                                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                                        <subviews>
                                                            <textView importsGraphics="NO" verticallyResizable="YES" usesFontPanel="YES" findStyle="panel" continuousSpellChecking="YES" allowsUndo="YES" usesRuler="YES" allowsNonContiguousLayout="YES" quoteSubstitution="YES" dashSubstitution="YES" spellingCorrection="YES" smartInsertDelete="YES" id="789-123-456">
                                                                <rect key="frame" x="0.0" y="0.0" width="698" height="328"/>
                                                                <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                                                <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                                <size key="minSize" width="698" height="328"/>
                                                                <size key="maxSize" width="700" height="10000000"/>
                                                                <color key="insertionPointColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                            </textView>
                                                        </subviews>
                                                    </clipView>
                                                    <scroller key="horizontalScroller" hidden="YES" wantsLayer="YES" verticalHuggingPriority="750" doubleValue="1" horizontal="YES" id="147-258-369">
                                                        <rect key="frame" x="1" y="313" width="698" height="16"/>
                                                        <autoresizingMask key="autoresizingMask"/>
                                                    </scroller>
                                                    <scroller key="verticalScroller" hidden="YES" wantsLayer="YES" verticalHuggingPriority="750" doubleValue="1" horizontal="NO" id="258-369-147">
                                                        <rect key="frame" x="683" y="1" width="16" height="328"/>
                                                        <autoresizingMask key="autoresizingMask"/>
                                                    </scroller>
                                                </scrollView>
                                            </subviews>
                                        </view>
                                    </tabViewItem>
                                    <tabViewItem label="Resource Update" identifier="update" id="Upd-ate-Tab">
                                        <view key="view" ambiguous="YES" id="Upd-ate-Vie">
                                            <rect key="frame" x="10" y="33" width="740" height="514"/>
                                            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                            <subviews>
                                                <textField focusRingType="none" horizontalHuggingPriority="251" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="Upd-Tit-le1">
                                                    <rect key="frame" x="18" y="474" width="704" height="22"/>
                                                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                    <textFieldCell key="cell" lineBreakMode="clipping" title="Update Resources (* indicates required field)" id="Upd-Tit-le2">
                                                        <font key="font" metaFont="systemBold" size="16"/>
                                                        <color key="textColor" name="labelColor" catalog="System" colorSpace="catalog"/>
                                                        <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                    </textFieldCell>
                                                </textField>
                                                <textField focusRingType="none" horizontalHuggingPriority="251" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="Upd-Des-c1">
                                                    <rect key="frame" x="18" y="440" width="704" height="30"/>
                                                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                    <textFieldCell key="cell" lineBreakMode="clipping" title="Update a single resource or batch update from CSV file. ResourceID is required for updates." id="Upd-Des-c2">
                                                        <font key="font" metaFont="system"/>
                                                        <color key="textColor" name="secondaryLabelColor" catalog="System" colorSpace="catalog"/>
                                                        <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                    </textFieldCell>
                                                </textField>
                                                <textField focusRingType="none" horizontalHuggingPriority="251" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="Upd-ResID-Lab">
                                                    <rect key="frame" x="18" y="414" width="80" height="17"/>
                                                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                    <textFieldCell key="cell" lineBreakMode="clipping" title="ResourceID*:" id="Upd-ResID-Lab2">
                                                        <font key="font" metaFont="system"/>
                                                        <color key="textColor" name="labelColor" catalog="System" colorSpace="catalog"/>
                                                        <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                    </textFieldCell>
                                                </textField>
                                                <textField focusRingType="none" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="Upd-ResID-TF">
                                                    <rect key="frame" x="110" y="412" width="120" height="22"/>
                                                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                    <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" selectable="YES" editable="YES" sendsActionOnEndEditing="YES" borderStyle="bezel" drawsBackground="YES" id="Upd-ResID-TFC">
                                                        <font key="font" metaFont="system"/>
                                                        <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                        <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                    </textFieldCell>
                                                </textField>
                                                <textField focusRingType="none" horizontalHuggingPriority="251" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="Upd-Title-Lab">
                                                    <rect key="frame" x="250" y="414" width="40" height="17"/>
                                                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                    <textFieldCell key="cell" lineBreakMode="clipping" title="Title:" id="Upd-Title-Lab2">
                                                        <font key="font" metaFont="system"/>
                                                        <color key="textColor" name="labelColor" catalog="System" colorSpace="catalog"/>
                                                        <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                    </textFieldCell>
                                                </textField>
                                                <textField focusRingType="none" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="Upd-Title-TF">
                                                    <rect key="frame" x="300" y="412" width="200" height="22"/>
                                                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                    <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" selectable="YES" editable="YES" sendsActionOnEndEditing="YES" borderStyle="bezel" drawsBackground="YES" id="Upd-Title-TFC">
                                                        <font key="font" metaFont="system"/>
                                                        <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                        <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                    </textFieldCell>
                                                </textField>
                                                <textField focusRingType="none" horizontalHuggingPriority="251" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="Upd-Pri-Lab">
                                                    <rect key="frame" x="520" y="414" width="50" height="17"/>
                                                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                    <textFieldCell key="cell" lineBreakMode="clipping" title="Priority:" id="Upd-Pri-Lab2">
                                                        <font key="font" metaFont="system"/>
                                                        <color key="textColor" name="labelColor" catalog="System" colorSpace="catalog"/>
                                                        <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                    </textFieldCell>
                                                </textField>
                                                <popUpButton verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="Upd-Pri-PopUp">
                                                    <rect key="frame" x="578" y="410" width="80" height="25"/>
                                                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                    <popUpButtonCell key="cell" type="push" title="N/A" bezelStyle="rounded" alignment="left" lineBreakMode="truncatingTail" state="on" borderStyle="borderAndBezel" imageScaling="proportionallyDown" inset="2" selectedItem="Upd-Pri-ItNA" id="Upd-Pri-PopUpCell">
                                                        <behavior key="behavior" lightByBackground="YES" lightByGray="YES"/>
                                                        <font key="font" metaFont="message"/>
                                                        <menu key="menu" id="Upd-Pri-Menu">
                                                            <items>
                                                                <menuItem title="N/A" state="on" id="Upd-Pri-ItNA"/>
                                                                <menuItem title="0" id="Upd-Pri-It0"/>
                                                                <menuItem title="1" id="Upd-Pri-It1"/>
                                                                <menuItem title="2" id="Upd-Pri-It2"/>
                                                                <menuItem title="3" id="Upd-Pri-It3"/>
                                                                <menuItem title="4" id="Upd-Pri-It4"/>
                                                                <menuItem title="5" id="Upd-Pri-It5"/>
                                                            </items>
                                                        </menu>
                                                    </popUpButtonCell>
                                                </popUpButton>
                                                <textField focusRingType="none" horizontalHuggingPriority="251" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="Upd-Loc-Lab">
                                                    <rect key="frame" x="18" y="384" width="60" height="17"/>
                                                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                    <textFieldCell key="cell" lineBreakMode="clipping" title="Location:" id="Upd-Loc-Lab2">
                                                        <font key="font" metaFont="system"/>
                                                        <color key="textColor" name="labelColor" catalog="System" colorSpace="catalog"/>
                                                        <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                    </textFieldCell>
                                                </textField>
                                                <textField focusRingType="none" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="Upd-Loc-TF">
                                                    <rect key="frame" x="88" y="382" width="100" height="22"/>
                                                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                    <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" selectable="YES" editable="YES" sendsActionOnEndEditing="YES" borderStyle="bezel" drawsBackground="YES" id="Upd-Loc-TFC">
                                                        <font key="font" metaFont="system"/>
                                                        <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                        <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                    </textFieldCell>
                                                </textField>
                                                <button verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="Upd-Single-Btn">
                                                    <rect key="frame" x="20" y="340" width="120" height="32"/>
                                                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                    <buttonCell key="cell" type="push" title="Update Resource" bezelStyle="rounded" alignment="center" borderStyle="border" imageScaling="proportionallyDown" inset="2" id="Upd-Single-Cel">
                                                        <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                                                        <font key="font" metaFont="system"/>
                                                    </buttonCell>
                                                    <connections>
                                                        <action selector="updateSingleResourceClicked:" target="XfG-lQ-9wD" id="Upd-Single-Act"/>
                                                    </connections>
                                                </button>
                                                <button verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="Upd-CSV-Btn">
                                                    <rect key="frame" x="160" y="340" width="150" height="32"/>
                                                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                    <buttonCell key="cell" type="push" title="Update from CSV" bezelStyle="rounded" alignment="center" borderStyle="border" imageScaling="proportionallyDown" inset="2" id="Upd-CSV-Cel">
                                                        <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                                                        <font key="font" metaFont="system"/>
                                                    </buttonCell>
                                                    <connections>
                                                        <action selector="updateCsvButtonClicked:" target="XfG-lQ-9wD" id="Upd-CSV-Act"/>
                                                    </connections>
                                                </button>
                                                <button verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="Upd-Tmp-Btn">
                                                    <rect key="frame" x="330" y="340" width="150" height="32"/>
                                                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                    <buttonCell key="cell" type="push" title="Download Template" bezelStyle="rounded" alignment="center" borderStyle="border" imageScaling="proportionallyDown" inset="2" id="Upd-Tmp-Cel">
                                                        <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                                                        <font key="font" metaFont="system"/>
                                                    </buttonCell>
                                                    <connections>
                                                        <action selector="downloadTemplateButtonClicked:" target="XfG-lQ-9wD" id="Upd-Tmp-Act"/>
                                                    </connections>
                                                </button>
                                                <button verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="Prt-Res-Btn2">
                                                    <rect key="frame" x="500" y="340" width="120" height="32"/>
                                                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                    <buttonCell key="cell" type="push" title="Print Resources" bezelStyle="rounded" alignment="center" borderStyle="border" imageScaling="proportionallyDown" inset="2" id="Prt-Res-Cel2">
                                                        <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                                                        <font key="font" metaFont="system"/>
                                                    </buttonCell>
                                                    <connections>
                                                        <action selector="printResourcesClicked:" target="XfG-lQ-9wD" id="Prt-Res-Act2"/>
                                                    </connections>
                                                </button>
                                                <scrollView fixedFrame="YES" autohidesScrollers="YES" horizontalLineScroll="10" horizontalPageScroll="10" verticalLineScroll="10" verticalPageScroll="10" usesPredominantAxisScrolling="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Upd-Log-Scr">
                                                    <rect key="frame" x="20" y="20" width="700" height="300"/>
                                                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                    <clipView key="contentView" ambiguous="YES" id="Upd-Log-Clp">
                                                        <rect key="frame" x="1" y="1" width="698" height="298"/>
                                                        <autoresizingMask key="autoresizingMask"/>
                                                        <subviews>
                                                            <textView ambiguous="YES" importsGraphics="NO" verticallyResizable="YES" usesFontPanel="YES" findStyle="panel" continuousSpellChecking="YES" allowsUndo="YES" usesRuler="YES" allowsNonContiguousLayout="YES" quoteSubstitution="YES" dashSubstitution="YES" spellingCorrection="YES" smartInsertDelete="YES" id="Upd-Log-Txt">
                                                                <rect key="frame" x="0.0" y="0.0" width="698" height="298"/>
                                                                <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                                                <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                                <size key="minSize" width="698" height="298"/>
                                                                <size key="maxSize" width="700" height="10000000"/>
                                                                <color key="insertionPointColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                            </textView>
                                                        </subviews>
                                                    </clipView>
                                                    <scroller key="horizontalScroller" hidden="YES" wantsLayer="YES" verticalHuggingPriority="750" doubleValue="1" horizontal="YES" id="Upd-Log-HSc">
                                                        <rect key="frame" x="1" y="283" width="698" height="16"/>
                                                        <autoresizingMask key="autoresizingMask"/>
                                                    </scroller>
                                                    <scroller key="verticalScroller" hidden="YES" wantsLayer="YES" verticalHuggingPriority="750" doubleValue="1" horizontal="NO" id="Upd-Log-VSc">
                                                        <rect key="frame" x="683" y="1" width="16" height="298"/>
                                                        <autoresizingMask key="autoresizingMask"/>
                                                    </scroller>
                                                </scrollView>
                                            </subviews>
                                        </view>
                                    </tabViewItem>
                                </tabViewItems>
                            </tabView>
                        </subviews>
                    </view>
                    <connections>
                        <outlet property="categoryIdTextField" destination="Cat-Tex-tF1" id="hhh-iii-jjj"/>
                        <outlet property="createButton" destination="234-567-890" id="aaa-bbb-ccc"/>
                        <outlet property="csvButton" destination="345-678-901" id="bbb-ccc-ddd"/>
                        <outlet property="downloadTemplateButton" destination="Upd-Tmp-Btn" id="iii-jjj-kkk"/>
                        <outlet property="locationTextField" destination="Loc-Tex-tF1" id="jjj-kkk-lll"/>
                        <outlet property="logTextView" destination="789-123-456" id="ccc-ddd-eee"/>

                        <outlet property="printResourcesButton" destination="Prt-Res-Btn1" id="ppp-qqq-rrr"/>
                        <outlet property="quantityTextField" destination="Qty-Tex-tF1" id="lll-mmm-nnn"/>
                        <outlet property="resdefIdTextField" destination="stu-vwx-yz1" id="eee-fff-ggg"/>
                        <outlet property="tabView" destination="dkI-za-hwg" id="fff-ggg-hhh"/>
                        <outlet property="titleTextField" destination="abc-def-ghi" id="ggg-hhh-iii"/>
                        <outlet property="updateCsvButton" destination="Upd-CSV-Btn" id="mmm-nnn-ooo"/>
                        <outlet property="updateLogTextView" destination="Upd-Log-Txt" id="nnn-ooo-ppp"/>
                        <outlet property="updateResourceIdTextField" destination="Upd-ResID-TF" id="upd-res-id-out"/>
                        <outlet property="updateTitleTextField" destination="Upd-Title-TF" id="upd-title-out"/>
                        <outlet property="updatePriorityPopUpButton" destination="Upd-Pri-PopUp" id="upd-pri-out"/>
                        <outlet property="updateLocationTextField" destination="Upd-Loc-TF" id="upd-loc-out"/>
                        <outlet property="updateSingleButton" destination="Upd-Single-Btn" id="upd-single-out"/>
                    </connections>
                </viewController>
                <customObject id="rfc-4N-Vw7" userLabel="First Responder" customClass="NSResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="75" y="655"/>
        </scene>
    </scenes>
</document>
