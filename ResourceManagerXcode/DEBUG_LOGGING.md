# Debug Logging Configuration

## Overview

The Resource Manager application includes a comprehensive debug logging system that can be easily enabled or disabled for troubleshooting purposes.

## How to Enable/Disable Debug Logging

### Locations
The debug logging configuration is located in two files:

1. **ResourceManager/ResourceManagerCore.swift** (for core functionality):
```swift
// MARK: - Debug Configuration
// Set to true to enable detailed debug logging
private let DEBUG_LOGGING = false
```

2. **ResourceManager/ViewController.swift** (for UI interactions):
```swift
// MARK: - Debug Configuration
// Set to true to enable detailed debug logging
private let DEBUG_LOGGING = false
```

### To Enable Debug Logging
1. Open both `ResourceManager/ResourceManagerCore.swift` and `ResourceManager/ViewController.swift`
2. Change `private let DEBUG_LOGGING = false` to `private let DEBUG_LOGGING = true` in both files
3. Rebuild the application

### To Disable Debug Logging
1. Open both `ResourceManager/ResourceManagerCore.swift` and `ResourceManager/ViewController.swift`
2. Change `private let DEBUG_LOGGING = true` to `private let DEBUG_LOGGING = false` in both files
3. Rebuild the application

## What Debug Logging Includes

When enabled, debug logging provides detailed information about:

### Resource Creation
- Input parameters validation
- API calls and responses
- Resource processing steps
- Success/failure status

### Resource Updates
- CSV file processing
- Individual resource update operations
- API request/response details
- Error handling

### Authentication
- Authentication process
- Token management
- API connection status

### UI Interactions (ViewController)
- Button click events
- File selection dialogs
- Parameter validation
- Task execution flow
- Response handling

### Print Operations
- Resource data processing
- File generation
- Output location

## Important Notes

1. **Performance Impact**: Debug logging may impact application performance due to extensive console output
2. **Sensitive Information**: Debug logs may contain API tokens and resource data - use only in development
3. **Error Messages**: Critical error messages are always displayed regardless of debug setting
4. **UI Feedback**: User-facing success/error messages are always shown regardless of debug setting

## Example Debug Output

When enabled, you'll see detailed logs like:

**Core functionality (ResourceManagerCore):**
```
🎯 ApplicationService.createSingleResource called
📥 Raw input parameters:
   title: 'nil'
   resdefId: '28130'
   priority: '5'
   quantity: '1'
   location: 'Aruba'
   categoryId: 'nil'
🔄 ApplicationService: Starting do-catch block...
🔄 ApplicationService: Inside do block, about to call resourceManager.createResource...
✅ ApplicationService: resourceManager.createResource returned successfully!
📊 Result: resourceId=12345, link=rdar://res/12345
```

**UI interactions (ViewController):**
```
🚀 Create Resource button clicked
📝 Creating resource with:
   Title: nil
   ResdefID: 28130
   Priority: 5
   Quantity: 1
   Location: Aruba
   CategoryID: nil
🔄 About to call ApplicationService.createSingleResource...
📥 Parameters: title=nil, resdefId=28130, priority=5, quantity=1, location=Aruba, categoryId=nil
🔍 ViewController: appService instance = Optional(ResourceManager.ApplicationService)
🔍 ViewController: About to call appService.createSingleResource...
🔄 ViewController: appService.createSingleResource returned
📊 ViewController: response.isSuccess = true
📊 ViewController: response.message = nil
📊 ViewController: response.errorCode = nil
```

When disabled, you'll only see essential user messages:
```
✅ Resource created successfully!
🔗 Resource ID: 12345
🔗 Resource Link: rdar://res/12345
✅ Created 3 resources from CSV
   Resource ID: 12345
   Resource Link: rdar://res/12345
   Resource ID: 12346
   Resource Link: rdar://res/12346
   Resource ID: 12347
   Resource Link: rdar://res/12347
✅ Successfully updated 2 resource(s)
🔗 Combined Resource Link: rdar://res/12345&12346
📋 Combined link copied to clipboard
✅ Print file generated: /Users/<USER>/Downloads/ResourcePrint_6-18-25, 9-43-07 AM.csv
✅ Template downloaded successfully
```
