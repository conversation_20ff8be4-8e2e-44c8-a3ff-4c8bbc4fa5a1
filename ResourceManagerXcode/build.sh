#!/bin/bash

echo "🚀 Building Resource Manager Xcode Project..."

# Navigate to project directory
cd "$(dirname "$0")"

# Build the project
xcodebuild -project ResourceManager.xcodeproj -scheme ResourceManager -configuration Debug build

if [ $? -eq 0 ]; then
    echo "✅ Build successful!"
    echo "🎯 You can now open ResourceManager.xcodeproj in Xcode"
    echo "📱 Or run the app directly from Xcode"
else
    echo "❌ Build failed!"
    exit 1
fi
