# -*- mode: python ; coding: utf-8 -*-
# Simple PyInstaller spec for ResourceManager - restored to initial state

block_cipher = None

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('core/', 'core/'),
        ('gui/', 'gui/'),
        ('utils/', 'utils/'),
        ('config.py', '.'),
    ],
    hiddenimports=[
        'wx',
        'wx.lib.newevent',
        'wx.lib.agw',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='ResourceManager',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='ResourceManager'
)

app = BUNDLE(
    coll,
    name='ResourceManager.app',
    icon=None,
    bundle_identifier='com.resourcemanager.app',
)
