# Resource Manager Python Package Manifest
# This file specifies which files to include in the distribution package

# Include documentation
include README_PYTHON.md
include LICENSE
include CHANGELOG.md

# Include configuration files
include config.py
include requirements_macos.txt
include ResourceManager.spec

# Include installation scripts
include install.sh
include setup.py

# Include all Python source files
recursive-include core *.py
recursive-include gui *.py
recursive-include utils *.py

# Include CSV templates and data files
recursive-include core *.csv
recursive-include templates *.csv
include test_update.csv
include test_update_with_data.csv

# Include assets and resources
recursive-include assets *
recursive-include core/img *

# Include build and distribution files
include ResourceManager.spec

# Exclude compiled Python files
global-exclude *.pyc
global-exclude *.pyo
global-exclude __pycache__
recursive-exclude * __pycache__
recursive-exclude * *.py[co]

# Exclude development and build artifacts
exclude .gitignore
exclude .DS_Store
recursive-exclude * .DS_Store
exclude *.log
recursive-exclude dist *
recursive-exclude build *
recursive-exclude venv *
recursive-exclude .git *

# Exclude IDE files
recursive-exclude * .vscode
recursive-exclude * .idea
recursive-exclude * *.swp
recursive-exclude * *.swo

# Include specific distribution files if they exist
include create_distribution.sh
include build.sh
