Resource Manager Python Distribution Package
===========================================

Package: resource_manager_python
Version: 1.0.0
Build Date: Mon Jun 23 10:25:42 CST 2025
Platform: macOS (Universal)
Python Version: 3.7+

CONTENTS:
- Complete Python source code
- templates/: CSV templates for resource operations
- documentation/: User and developer documentation
- scripts/: Installation and utility scripts

FEATURES:
- Resource creation and management
- CSV import/export functionality
- GUI interface with wxPython
- API integration with Apple TSTT Resource API
- Kerberos authentication
- Comprehensive error handling
- Debug logging capabilities

ARCHITECTURE:
- Clean architecture with separation of concerns
- Dependency injection for testability
- Standardized API response handling
- Modular design for maintainability

DEPENDENCIES:
- wxPython (GUI framework)
- requests (HTTP client)
- pykerberos (Authentication)
- Standard Python libraries

BUILD INFORMATION:
- Built on: macOS 26.0
- Python: Python 3.9.6
- Architecture: arm64

For more information, see documentation/README_PYTHON.md
