"""
Simplified Data Access Layer Module for macOS App
"""

import csv
from pathlib import Path
from typing import Dict, List, Optional, Any, Union
from utils.csv_handler import CSVHandler

from core.interfaces import ResourceClientInterface, FileHandlerInterface
from utils.file_path_manager import file_path_manager
import config


class DataAccessLayer:
    """Simplified data access layer for handling file operations."""
    
    def __init__(self, resource_client: ResourceClientInterface):
        """Initialize DataAccessLayer."""
        self.resource_client = resource_client
    
    def read_csv_data(self, filepath: Union[str, Path]) -> List[Dict[str, Any]]:
        """Read CSV file and return list of dictionaries."""
        if isinstance(filepath, str):
            file_path = file_path_manager.get_csv_file_path(filepath)
        else:
            file_path = filepath
        return CSVHandler.read_csv(str(file_path))
    
    def write_csv_data(self, header: List[str], data: List[List[Any]], filepath: Union[str, Path]) -> None:
        """Write header and data to CSV file."""
        if isinstance(filepath, str):
            # Use the correct path for resource_dup.csv
            if filepath == 'resource_dup.csv':
                file_path = file_path_manager.get_resource_dup_csv_path()
            else:
                file_path = file_path_manager.get_output_file_path(filepath)
        else:
            file_path = filepath

        # Combine header and data for writing
        combined_data = [header] + data
        CSVHandler.write_csv(combined_data, str(file_path))
        print(f"Data written to {file_path}, {len(data)} rows")
    
    def process_print_data(self, filename: str = 'resource_dup.csv') -> str:
        """Process data for printing and return output file path."""
        # Read the CSV file
        data = self.read_csv_data(filename)
        
        if not data:
            raise Exception(f"No data found in {filename}")
        
        # Since data is now list of lists, we need to handle it differently
        header = data[0]  # First row is header
        rows = data[1:]   # Rest are data rows

        # Process each row
        for row in rows:
            # Find column indices
            try:
                resdef_idx = header.index('resdef*') if 'resdef*' in header else -1
                resource_id_idx = header.index('resourceID*') if 'resourceID*' in header else -1
                pri_idx = header.index('Pri') if 'Pri' in header else -1

                # Add resdef_link if resdef* exists and is not empty
                if resdef_idx >= 0 and len(row) > resdef_idx:
                    resdef_value = str(row[resdef_idx]).strip()
                    if resdef_value and resdef_value.lower() != 'nan':
                        # Add resdef_link column if not exists
                        if 'resdef_link' not in header:
                            header.append('resdef_link')
                        # Ensure row has enough elements
                        while len(row) < len(header):
                            row.append('')
                        row[header.index('resdef_link')] = f"rdar://resdef/{resdef_value}"

                # Add res_link
                if resource_id_idx >= 0 and len(row) > resource_id_idx:
                    resource_id = str(row[resource_id_idx]).strip()
                    if resource_id:
                        # Add res_link column if not exists
                        if 'res_link' not in header:
                            header.append('res_link')
                        # Ensure row has enough elements
                        while len(row) < len(header):
                            row.append('')
                        row[header.index('res_link')] = f"rdar://res/{resource_id}"

                # Convert Priority
                if pri_idx >= 0 and len(row) > pri_idx:
                    import config
                    pri_value = str(row[pri_idx]).strip()
                    if 'Priority' not in header:
                        header.append('Priority')
                    # Ensure row has enough elements
                    while len(row) < len(header):
                        row.append('')

                    if pri_value == str(config.PRIORITY_NA_VALUE):
                        row[header.index('Priority')] = 'N/A'
                    elif pri_value.isdigit():
                        row[header.index('Priority')] = str(int(pri_value) - 1)
                    else:
                        row[header.index('Priority')] = pri_value

            except Exception as e:
                print(f"Error processing row: {e}")
                continue
        
        # If there's only one data row, duplicate it
        if len(rows) == 1:
            rows.append(rows[0].copy())

        # Generate output file path
        output_file = file_path_manager.get_label_print_output_path()

        # Save to CSV
        self.write_csv_data(header, rows, output_file)
        
        return str(output_file)
    
    def clean_duplicate_file(self, filename: str = 'resource_dup.csv') -> None:
        """Remove duplicate file if it exists."""
        if filename == 'resource_dup.csv':
            file_path = file_path_manager.get_resource_dup_csv_path()
        else:
            file_path = file_path_manager.get_csv_file_path(filename)

        file_path_manager.remove_file_if_exists(file_path)

    def ensure_file_exists(self, filename: str) -> Path:
        """Ensure a file exists, create it if it doesn't."""
        if filename == 'resource_dup.csv':
            file_path = file_path_manager.get_resource_dup_csv_path()
        else:
            file_path = file_path_manager.get_csv_file_path(filename)

        # Create parent directories if they don't exist
        file_path.parent.mkdir(parents=True, exist_ok=True)

        # Create empty file if it doesn't exist
        if not file_path.exists():
            file_path.touch()

        return file_path
    
    def validate_csv_structure(self, filename: str, required_columns: List[str]) -> bool:
        """Validate that CSV file has required columns."""
        try:
            data = self.read_csv_data(filename)
            if not data:
                return False
            # data[0] is now the header row (list of column names)
            columns = data[0] if isinstance(data[0], list) else list(data[0].keys())
            return all(col in columns for col in required_columns)
        except Exception:
            return False

    def get_resource_fields(self, resource_id: int, fields: List[str]) -> Dict[str, Any]:
        """Get specific fields for a resource from the API."""
        try:
            # Use the resource client to get resource details
            resource_data = self.resource_client.fields_for_resource_id(resource_id, request_fields=fields)
            if resource_data:
                return resource_data
            else:
                return {field: '' for field in fields}
        except Exception as e:
            print(f"Error getting resource fields for {resource_id}: {e}")
            return {field: '' for field in fields}

    def update_resource(self, resource_id: int, resource_data: Dict[str, Any]) -> Dict[str, Any]:
        """Update a resource using the API."""
        try:
            # Use the resource client to update the resource
            result = self.resource_client.update_resource(resource_id, resource_data)
            return result
        except Exception as e:
            print(f"Error updating resource {resource_id}: {e}")
            raise e

    def find_resource_definitions(self, criteria: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Find resource definitions based on criteria."""
        try:
            return self.resource_client.find_resource_definitions(criteria)
        except Exception as e:
            print(f"Error finding resource definitions: {e}")
            return []

    def get_resource_definition_by_id(self, resdef_id: int) -> Dict[str, Any]:
        """Get resource definition by ID."""
        try:
            return self.resource_client.resource_definition_for_id(resdef_id)
        except Exception as e:
            print(f"Error getting resource definition {resdef_id}: {e}")
            return {}

    def get_resource_by_id(self, resource_id: int) -> Dict[str, Any]:
        """Get resource by ID."""
        try:
            return self.resource_client.resource_for_id(resource_id)
        except Exception as e:
            print(f"Error getting resource {resource_id}: {e}")
            return {}

    def create_resource(self, resource_data: Dict[str, Any]) -> Any:
        """Create a new resource."""
        try:
            return self.resource_client.create_resource(resource_data)
        except Exception as e:
            print(f"Error creating resource: {e}")
            raise e

    def get_resource_definition_fields(self, resdef_id: int, fields: List[str]) -> Dict[str, Any]:
        """Get specific fields for a resource definition."""
        try:
            return self.resource_client.fields_for_resource_definition_id(resdef_id, request_fields=fields)
        except Exception as e:
            print(f"Error getting resource definition fields for {resdef_id}: {e}")
            return {field: '' for field in fields}


# Global instance
def create_data_access_layer(resource_client: ResourceClientInterface) -> DataAccessLayer:
    """Factory function to create DataAccessLayer instance."""
    return DataAccessLayer(resource_client)
