"""
Response models and result types for the Resource Manager application.

This module defines standardized response formats and result types
to ensure consistent API responses throughout the application.
"""

from typing import TypeVar, Generic, Optional, List, Dict, Any
from dataclasses import dataclass
from enum import Enum


class ResultStatus(Enum):
    """Enumeration of possible result statuses."""
    SUCCESS = "success"
    ERROR = "error"
    WARNING = "warning"
    PARTIAL_SUCCESS = "partial_success"


class ErrorCode(Enum):
    """Enumeration of error codes."""
    VALIDATION_ERROR = "VALIDATION_ERROR"
    RESOURCE_NOT_FOUND = "RESOURCE_NOT_FOUND"
    PERMISSION_DENIED = "PERMISSION_DENIED"
    NETWORK_ERROR = "NETWORK_ERROR"
    FILE_ERROR = "FILE_ERROR"
    API_ERROR = "API_ERROR"
    UNKNOWN_ERROR = "UNKNOWN_ERROR"
    CATEGORY_ID_ERROR = "CATEGORY_ID_ERROR"
    CSV_FORMAT_ERROR = "CSV_FORMAT_ERROR"


T = TypeVar('T')


@dataclass
class ApiResponse(Generic[T]):
    """
    Standardized API response format.
    
    This class provides a consistent structure for all API responses,
    including success and error cases.
    """
    status: ResultStatus
    data: Optional[T] = None
    message: Optional[str] = None
    error_code: Optional[ErrorCode] = None
    errors: Optional[List[str]] = None
    metadata: Optional[Dict[str, Any]] = None
    
    @classmethod
    def success(cls, data: T, message: Optional[str] = None, metadata: Optional[Dict[str, Any]] = None) -> 'ApiResponse[T]':
        """Create a successful response."""
        return cls(
            status=ResultStatus.SUCCESS,
            data=data,
            message=message,
            metadata=metadata
        )
    
    @classmethod
    def error(cls, message: str, error_code: ErrorCode = ErrorCode.UNKNOWN_ERROR, 
              errors: Optional[List[str]] = None, metadata: Optional[Dict[str, Any]] = None) -> 'ApiResponse[T]':
        """Create an error response."""
        return cls(
            status=ResultStatus.ERROR,
            message=message,
            error_code=error_code,
            errors=errors,
            metadata=metadata
        )
    
    @classmethod
    def warning(cls, data: T, message: str, metadata: Optional[Dict[str, Any]] = None) -> 'ApiResponse[T]':
        """Create a warning response."""
        return cls(
            status=ResultStatus.WARNING,
            data=data,
            message=message,
            metadata=metadata
        )
    
    @classmethod
    def partial_success(cls, data: T, message: str, errors: Optional[List[str]] = None,
                       metadata: Optional[Dict[str, Any]] = None) -> 'ApiResponse[T]':
        """Create a partial success response."""
        return cls(
            status=ResultStatus.PARTIAL_SUCCESS,
            data=data,
            message=message,
            errors=errors,
            metadata=metadata
        )
    
    def is_success(self) -> bool:
        """Check if the response indicates success."""
        return self.status in [ResultStatus.SUCCESS, ResultStatus.WARNING, ResultStatus.PARTIAL_SUCCESS]
    
    def is_error(self) -> bool:
        """Check if the response indicates an error."""
        return self.status == ResultStatus.ERROR
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert response to dictionary."""
        result = {
            'status': self.status.value,
            'data': self.data,
            'message': self.message
        }
        
        if self.error_code:
            result['error_code'] = self.error_code.value
        
        if self.errors:
            result['errors'] = self.errors
        
        if self.metadata:
            result['metadata'] = self.metadata
        
        return result


@dataclass
class ResourceData:
    """Data structure for resource information."""
    resource_id: str
    res_link: str
    title: str
    priority: Optional[int] = None
    resdef_id: Optional[str] = None
    location: Optional[str] = None
    found: bool = False
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            'resourceID*': self.resource_id,
            'res_link': self.res_link,
            'Title*': self.title,
            'Pri': self.priority,
            'resdef*': self.resdef_id,
            'location': self.location,
            'found': self.found
        }


@dataclass
class OperationResult:
    """Result of a resource operation."""
    success_count: int = 0
    error_count: int = 0
    warning_count: int = 0
    total_count: int = 0
    resources: List[ResourceData] = None
    errors: List[str] = None
    warnings: List[str] = None
    
    def __post_init__(self):
        if self.resources is None:
            self.resources = []
        if self.errors is None:
            self.errors = []
        if self.warnings is None:
            self.warnings = []
    
    def add_success(self, resource: ResourceData):
        """Add a successful resource operation."""
        self.resources.append(resource)
        self.success_count += 1
        self.total_count += 1
    
    def add_error(self, error_message: str):
        """Add an error."""
        self.errors.append(error_message)
        self.error_count += 1
        self.total_count += 1
    
    def add_warning(self, warning_message: str):
        """Add a warning."""
        self.warnings.append(warning_message)
        self.warning_count += 1
    
    def get_status(self) -> ResultStatus:
        """Determine the overall status of the operation."""
        if self.error_count > 0 and self.success_count == 0:
            return ResultStatus.ERROR
        elif self.error_count > 0 and self.success_count > 0:
            return ResultStatus.PARTIAL_SUCCESS
        elif self.warning_count > 0:
            return ResultStatus.WARNING
        else:
            return ResultStatus.SUCCESS
    
    def get_summary_message(self) -> str:
        """Get a summary message of the operation."""
        if self.total_count == 0:
            return "No operations performed"
        
        parts = []
        if self.success_count > 0:
            parts.append(f"{self.success_count} successful")
        if self.error_count > 0:
            parts.append(f"{self.error_count} failed")
        if self.warning_count > 0:
            parts.append(f"{self.warning_count} warnings")
        
        return f"Operation completed: {', '.join(parts)} out of {self.total_count} total"
    
    def to_api_response(self) -> ApiResponse[List[ResourceData]]:
        """Convert to standardized API response."""
        status = self.get_status()
        message = self.get_summary_message()
        
        metadata = {
            'success_count': self.success_count,
            'error_count': self.error_count,
            'warning_count': self.warning_count,
            'total_count': self.total_count
        }
        
        if status == ResultStatus.ERROR:
            return ApiResponse.error(
                message=message,
                errors=self.errors,
                metadata=metadata
            )
        elif status == ResultStatus.PARTIAL_SUCCESS:
            return ApiResponse.partial_success(
                data=self.resources,
                message=message,
                errors=self.errors,
                metadata=metadata
            )
        elif status == ResultStatus.WARNING:
            return ApiResponse.warning(
                data=self.resources,
                message=message,
                metadata=metadata
            )
        else:
            return ApiResponse.success(
                data=self.resources,
                message=message,
                metadata=metadata
            )


# Type aliases for common response types
ResourceResponse = ApiResponse[ResourceData]
ResourceListResponse = ApiResponse[List[ResourceData]]
StringResponse = ApiResponse[str]
BoolResponse = ApiResponse[bool]
DictResponse = ApiResponse[Dict[str, Any]]

# Factory functions for common responses
def success_response(data: T, message: Optional[str] = None) -> ApiResponse[T]:
    """Create a success response."""
    return ApiResponse.success(data, message)

def error_response(message: str, error_code: ErrorCode = ErrorCode.UNKNOWN_ERROR) -> ApiResponse[None]:
    """Create an error response."""
    return ApiResponse.error(message, error_code)

def validation_error_response(message: str, errors: List[str] = None) -> ApiResponse[None]:
    """Create a validation error response."""
    return ApiResponse.error(message, ErrorCode.VALIDATION_ERROR, errors)

def file_error_response(message: str) -> ApiResponse[None]:
    """Create a file error response."""
    return ApiResponse.error(message, ErrorCode.FILE_ERROR)

def api_error_response(message: str) -> ApiResponse[None]:
    """Create an API error response."""
    return ApiResponse.error(message, ErrorCode.API_ERROR)
