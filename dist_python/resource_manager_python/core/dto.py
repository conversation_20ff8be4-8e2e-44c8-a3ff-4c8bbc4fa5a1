"""
Data Transfer Objects (DTOs) for Resource Manager application.

This module defines DTOs that are used to transfer data between
different layers of the application, particularly between the
GUI and the application service layer.
"""

from dataclasses import dataclass
from typing import Optional, List, Dict, Any
from datetime import datetime


@dataclass
class CreateResourceRequest:
    """DTO for resource creation requests."""
    title: Optional[str] = None
    resdef_id: Optional[str] = None
    priority: Optional[str] = None
    quantity: Optional[str] = None
    location: Optional[str] = None
    category_id: Optional[str] = None
    
    def validate(self) -> List[str]:
        """Validate the request and return list of errors."""
        errors = []
        
        if not self.title and not self.resdef_id:
            errors.append("Either title or resource definition ID must be provided")
        
        if not self.quantity:
            errors.append("Quantity must be provided")
        elif not self.quantity.isdigit() or int(self.quantity) <= 0:
            errors.append("Quantity must be a positive integer")
        
        if self.priority and not self.priority.isdigit():
            errors.append("Priority must be a number")
        
        return errors
    
    def is_valid(self) -> bool:
        """Check if the request is valid."""
        return len(self.validate()) == 0


@dataclass
class UpdateResourceRequest:
    """DTO for resource update requests."""
    resource_id: str
    title: Optional[str] = None
    priority: Optional[str] = None
    resdef_id: Optional[str] = None
    location: Optional[str] = None
    
    def validate(self) -> List[str]:
        """Validate the request and return list of errors."""
        errors = []
        
        if not self.resource_id:
            errors.append("Resource ID must be provided")
        
        if self.priority and not self.priority.isdigit():
            errors.append("Priority must be a number")
        
        return errors
    
    def is_valid(self) -> bool:
        """Check if the request is valid."""
        return len(self.validate()) == 0


@dataclass
class ResourceInfo:
    """DTO for resource information."""
    resource_id: str
    title: str
    priority: Optional[int] = None
    resdef_id: Optional[str] = None
    resdef_link: Optional[str] = None
    res_link: Optional[str] = None
    location: Optional[str] = None
    status: Optional[str] = None
    found: bool = False
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ResourceInfo':
        """Create ResourceInfo from dictionary."""
        return cls(
            resource_id=str(data.get('resourceID*', '')),
            title=str(data.get('Title*', '')),
            priority=data.get('Pri'),
            resdef_id=str(data.get('resdef*', '')) if data.get('resdef*') else None,
            resdef_link=data.get('resdef_link'),
            res_link=data.get('res_link'),
            location=data.get('location'),
            status=data.get('status'),
            found=data.get('found', False)
        )
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            'resourceID*': self.resource_id,
            'Title*': self.title,
            'Pri': self.priority,
            'resdef*': self.resdef_id,
            'resdef_link': self.resdef_link,
            'res_link': self.res_link,
            'location': self.location,
            'status': self.status,
            'found': self.found
        }


@dataclass
class FileOperationRequest:
    """DTO for file operation requests."""
    file_path: str
    operation_type: str  # 'create', 'update', 'print'
    backup_original: bool = True
    
    def validate(self) -> List[str]:
        """Validate the request and return list of errors."""
        errors = []
        
        if not self.file_path:
            errors.append("File path must be provided")
        
        if self.operation_type not in ['create', 'update', 'print']:
            errors.append("Operation type must be 'create', 'update', or 'print'")
        
        return errors
    
    def is_valid(self) -> bool:
        """Check if the request is valid."""
        return len(self.validate()) == 0


@dataclass
class OperationStatus:
    """DTO for operation status updates."""
    operation_id: str
    operation_type: str
    status: str  # 'started', 'in_progress', 'completed', 'failed'
    progress: Optional[int] = None  # 0-100
    message: Optional[str] = None
    details: Optional[Dict[str, Any]] = None
    timestamp: Optional[datetime] = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()


@dataclass
class ValidationResult:
    """DTO for validation results."""
    is_valid: bool
    errors: List[str]
    warnings: List[str]
    field_errors: Dict[str, List[str]]
    
    def __init__(self):
        self.is_valid = True
        self.errors = []
        self.warnings = []
        self.field_errors = {}
    
    def add_error(self, message: str, field: Optional[str] = None):
        """Add an error."""
        self.is_valid = False
        self.errors.append(message)
        if field:
            if field not in self.field_errors:
                self.field_errors[field] = []
            self.field_errors[field].append(message)
    
    def add_warning(self, message: str):
        """Add a warning."""
        self.warnings.append(message)
    
    def has_errors(self) -> bool:
        """Check if there are any errors."""
        return len(self.errors) > 0
    
    def has_warnings(self) -> bool:
        """Check if there are any warnings."""
        return len(self.warnings) > 0


@dataclass
class ResourceCountInfo:
    """DTO for resource count information."""
    total_resources: int = 0
    active_resources: int = 0
    pending_resources: int = 0
    completed_resources: int = 0
    failed_resources: int = 0
    last_updated: Optional[datetime] = None
    
    def __post_init__(self):
        if self.last_updated is None:
            self.last_updated = datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            'total_resources': self.total_resources,
            'active_resources': self.active_resources,
            'pending_resources': self.pending_resources,
            'completed_resources': self.completed_resources,
            'failed_resources': self.failed_resources,
            'last_updated': self.last_updated.isoformat() if self.last_updated else None
        }


@dataclass
class PrintRequest:
    """DTO for print operation requests."""
    input_file: Optional[str] = None
    output_format: str = 'csv'
    include_links: bool = True
    duplicate_single_row: bool = True
    
    def validate(self) -> List[str]:
        """Validate the request and return list of errors."""
        errors = []
        
        if self.output_format not in ['csv', 'xlsx']:
            errors.append("Output format must be 'csv' or 'xlsx'")
        
        return errors
    
    def is_valid(self) -> bool:
        """Check if the request is valid."""
        return len(self.validate()) == 0


@dataclass
class ApplicationEvent:
    """DTO for application events."""
    event_type: str
    event_data: Dict[str, Any]
    timestamp: datetime
    source: Optional[str] = None
    
    def __init__(self, event_type: str, event_data: Dict[str, Any], source: Optional[str] = None):
        self.event_type = event_type
        self.event_data = event_data
        self.source = source
        self.timestamp = datetime.now()


# Factory functions for common DTOs
def create_resource_request(title: str = None, resdef_id: str = None, 
                          priority: str = None, quantity: str = None,
                          location: str = None, category_id: str = None) -> CreateResourceRequest:
    """Factory function to create CreateResourceRequest."""
    return CreateResourceRequest(
        title=title,
        resdef_id=resdef_id,
        priority=priority,
        quantity=quantity,
        location=location,
        category_id=category_id
    )


def create_file_operation_request(file_path: str, operation_type: str, 
                                backup_original: bool = True) -> FileOperationRequest:
    """Factory function to create FileOperationRequest."""
    return FileOperationRequest(
        file_path=file_path,
        operation_type=operation_type,
        backup_original=backup_original
    )


def create_print_request(input_file: str = None, output_format: str = 'csv',
                        include_links: bool = True, duplicate_single_row: bool = True) -> PrintRequest:
    """Factory function to create PrintRequest."""
    return PrintRequest(
        input_file=input_file,
        output_format=output_format,
        include_links=include_links,
        duplicate_single_row=duplicate_single_row
    )
