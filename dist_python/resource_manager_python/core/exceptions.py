"""
Custom exceptions for the Resource Manager application.

This module defines a hierarchy of custom exceptions to provide
better error handling and more specific error information.
"""

from typing import Optional, List, Dict, Any


class ResourceManagerException(Exception):
    """Base exception class for Resource Manager application."""
    
    def __init__(self, message: str, error_code: Optional[str] = None, 
                 details: Optional[Dict[str, Any]] = None):
        """
        Initialize ResourceManagerException.
        
        Args:
            message: Human-readable error message
            error_code: Machine-readable error code
            details: Additional error details
        """
        super().__init__(message)
        self.message = message
        self.error_code = error_code or self.__class__.__name__
        self.details = details or {}
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert exception to dictionary."""
        return {
            'error_type': self.__class__.__name__,
            'error_code': self.error_code,
            'message': self.message,
            'details': self.details
        }


class ValidationError(ResourceManagerException):
    """Exception raised for validation errors."""
    
    def __init__(self, message: str, field: Optional[str] = None, 
                 validation_errors: Optional[List[str]] = None):
        """
        Initialize ValidationError.
        
        Args:
            message: Error message
            field: Field that failed validation
            validation_errors: List of specific validation errors
        """
        details = {}
        if field:
            details['field'] = field
        if validation_errors:
            details['validation_errors'] = validation_errors
        
        super().__init__(message, 'VALIDATION_ERROR', details)
        self.field = field
        self.validation_errors = validation_errors or []


class ResourceNotFoundError(ResourceManagerException):
    """Exception raised when a resource is not found."""
    
    def __init__(self, resource_id: str, resource_type: str = "resource"):
        """
        Initialize ResourceNotFoundError.
        
        Args:
            resource_id: ID of the resource that was not found
            resource_type: Type of resource (resource, resource_definition, etc.)
        """
        message = f"{resource_type.title()} with ID '{resource_id}' not found"
        details = {
            'resource_id': resource_id,
            'resource_type': resource_type
        }
        super().__init__(message, 'RESOURCE_NOT_FOUND', details)
        self.resource_id = resource_id
        self.resource_type = resource_type


class CategoryIdError(ResourceManagerException):
    """Exception raised when category ID cannot be determined."""
    
    def __init__(self, resdef_id: Optional[str] = None):
        """
        Initialize CategoryIdError.
        
        Args:
            resdef_id: Resource definition ID that caused the error
        """
        message = "Could not get category ID from resource definition. Please provide category ID manually."
        details = {}
        if resdef_id:
            details['resdef_id'] = resdef_id
        
        super().__init__(message, 'CATEGORY_ID_ERROR', details)
        self.resdef_id = resdef_id


class FileOperationError(ResourceManagerException):
    """Exception raised for file operation errors."""
    
    def __init__(self, message: str, file_path: Optional[str] = None, 
                 operation: Optional[str] = None):
        """
        Initialize FileOperationError.
        
        Args:
            message: Error message
            file_path: Path to the file that caused the error
            operation: Operation that failed (read, write, delete, etc.)
        """
        details = {}
        if file_path:
            details['file_path'] = file_path
        if operation:
            details['operation'] = operation
        
        super().__init__(message, 'FILE_ERROR', details)
        self.file_path = file_path
        self.operation = operation


class CSVFormatError(FileOperationError):
    """Exception raised for CSV format errors."""
    
    def __init__(self, message: str, file_path: Optional[str] = None, 
                 line_number: Optional[int] = None, missing_columns: Optional[List[str]] = None):
        """
        Initialize CSVFormatError.
        
        Args:
            message: Error message
            file_path: Path to the CSV file
            line_number: Line number where error occurred
            missing_columns: List of missing required columns
        """
        details = {}
        if line_number:
            details['line_number'] = line_number
        if missing_columns:
            details['missing_columns'] = missing_columns
        
        super().__init__(message, file_path, 'csv_parse')
        self.error_code = 'CSV_FORMAT_ERROR'
        self.line_number = line_number
        self.missing_columns = missing_columns or []


class APIError(ResourceManagerException):
    """Exception raised for API-related errors."""
    
    def __init__(self, message: str, status_code: Optional[int] = None, 
                 response_data: Optional[Dict[str, Any]] = None, endpoint: Optional[str] = None):
        """
        Initialize APIError.
        
        Args:
            message: Error message
            status_code: HTTP status code
            response_data: Response data from the API
            endpoint: API endpoint that caused the error
        """
        details = {}
        if status_code:
            details['status_code'] = status_code
        if response_data:
            details['response_data'] = response_data
        if endpoint:
            details['endpoint'] = endpoint
        
        super().__init__(message, 'API_ERROR', details)
        self.status_code = status_code
        self.response_data = response_data
        self.endpoint = endpoint


class NetworkError(ResourceManagerException):
    """Exception raised for network-related errors."""
    
    def __init__(self, message: str, original_exception: Optional[Exception] = None):
        """
        Initialize NetworkError.
        
        Args:
            message: Error message
            original_exception: Original exception that caused this error
        """
        details = {}
        if original_exception:
            details['original_error'] = str(original_exception)
            details['original_error_type'] = type(original_exception).__name__
        
        super().__init__(message, 'NETWORK_ERROR', details)
        self.original_exception = original_exception


class AuthenticationError(ResourceManagerException):
    """Exception raised for authentication errors."""
    
    def __init__(self, message: str = "Authentication failed"):
        """
        Initialize AuthenticationError.
        
        Args:
            message: Error message
        """
        super().__init__(message, 'AUTHENTICATION_ERROR')


class PermissionError(ResourceManagerException):
    """Exception raised for permission errors."""
    
    def __init__(self, message: str, resource_id: Optional[str] = None, 
                 required_permission: Optional[str] = None):
        """
        Initialize PermissionError.
        
        Args:
            message: Error message
            resource_id: ID of the resource that requires permission
            required_permission: Permission that is required
        """
        details = {}
        if resource_id:
            details['resource_id'] = resource_id
        if required_permission:
            details['required_permission'] = required_permission
        
        super().__init__(message, 'PERMISSION_DENIED', details)
        self.resource_id = resource_id
        self.required_permission = required_permission


class ConfigurationError(ResourceManagerException):
    """Exception raised for configuration errors."""
    
    def __init__(self, message: str, config_key: Optional[str] = None):
        """
        Initialize ConfigurationError.
        
        Args:
            message: Error message
            config_key: Configuration key that caused the error
        """
        details = {}
        if config_key:
            details['config_key'] = config_key
        
        super().__init__(message, 'CONFIGURATION_ERROR', details)
        self.config_key = config_key


# Exception handler decorator
def handle_exceptions(default_return=None, log_errors=True):
    """
    Decorator to handle exceptions and convert them to standardized responses.
    
    Args:
        default_return: Default value to return on error
        log_errors: Whether to log errors
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except ResourceManagerException as e:
                if log_errors:
                    # Log the error (would need to import logging)
                    pass
                return default_return
            except Exception as e:
                if log_errors:
                    # Log the unexpected error
                    pass
                # Convert to ResourceManagerException
                raise ResourceManagerException(
                    f"Unexpected error: {str(e)}",
                    'UNKNOWN_ERROR',
                    {'original_error': str(e), 'original_error_type': type(e).__name__}
                )
        return wrapper
    return decorator


# Utility functions for exception handling
def is_retryable_error(exception: Exception) -> bool:
    """
    Determine if an error is retryable.
    
    Args:
        exception: Exception to check
        
    Returns:
        True if the error is retryable, False otherwise
    """
    retryable_types = (NetworkError, APIError)
    
    if isinstance(exception, retryable_types):
        return True
    
    if isinstance(exception, APIError) and exception.status_code:
        # Retry on server errors (5xx) but not client errors (4xx)
        return 500 <= exception.status_code < 600
    
    return False


def get_error_severity(exception: Exception) -> str:
    """
    Get the severity level of an error.
    
    Args:
        exception: Exception to evaluate
        
    Returns:
        Severity level: 'low', 'medium', 'high', 'critical'
    """
    if isinstance(exception, (ValidationError, CSVFormatError)):
        return 'low'
    elif isinstance(exception, (FileOperationError, ResourceNotFoundError)):
        return 'medium'
    elif isinstance(exception, (APIError, NetworkError)):
        return 'high'
    elif isinstance(exception, (AuthenticationError, PermissionError, ConfigurationError)):
        return 'critical'
    else:
        return 'medium'
