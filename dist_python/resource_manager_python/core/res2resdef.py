import re
import csv
import urllib3
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
from core.resource_client import TSTTResourceClient
resource_client = TSTTResourceClient()

def read_csv(filepath):
  with open(filepath, 'r') as csv_file:
    reader = csv.reader(csv_file)
    return list(reader)

def write_csv(header, data, filepath):
  with open(filepath, 'w', newline='') as csv_file:
    writer = csv.writer(csv_file)
    writer.writerow(header)
    writer.writerows(data)

def extract_resdef(speed_resdef):
  try:   
      match = re.search(r'\d{5}',speed_resdef)
      if match:
          resdef_id = match.group()
      return(resdef_id)
  except:
    pass

def res2resdef(resource_ID,resdef_id):
    resource_ID = resource_ID.replace("rdar://res/",'')
    # print(resource_ID)
    resource_ID = int(resource_ID)
    title = resource_client.fields_for_resource_id(int(resource_ID),request_fields=['keywords','title'])['title']
    # resdef_id = extract_resdef(title)
    resource = {        
        "resources": {
            "upsert": [
            {
                "id": resource_ID,
                "include": True
            }
            ]
        }
    }
    # print(resource)
    try:
        resource_client.update_resource_definition(int(resdef_id), resource)
        print(f"{resource_ID}:{title} attached to {resdef_id}")
    except Exception as e:
        if 'already attached to the resource ' in str(e):
           print(f"{resource_ID}:{title} attached to {resdef_id}")
        else:
            print(f"Error update resource: {e}")
    
def main():
    data = read_csv('resource2resdef.csv')
    header = data[0]
    data = data[1:]
    for row in data:
       resdef_id = row[4]
       resouceID = row[6]
       res2resdef(resouceID,resdef_id)

if __name__ == "__main__":
  main()
    