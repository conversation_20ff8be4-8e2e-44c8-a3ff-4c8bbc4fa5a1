#!/usr/bin/env python
import datetime
import json
import logging

import kerberos
import uuid
from collections import namedtuple

import requests

radar_server = "radar-webservices.apple.com"
tstt_server = "resources-radar-int.corp.apple.com"

ENV_PRODUCTION = "PRODUCTION"
ENV_UAT = "UAT"

ResourceAttachment = namedtuple('ResourceAttachment', ['name', 'content_type', 'data'])



def set_env(env):
    global radar_server, tstt_server
    if env == ENV_PRODUCTION:
        radar_server = "radar-webservices.apple.com"
        tstt_server = "resources-radar-int.corp.apple.com"
    elif env == ENV_UAT:
        radar_server = "bugreport-test-new.apple.com"
        tstt_server = "rdr-res-dev.corp.apple.com"


def raise_error(response):
    raise Exception("\33[91mError %i: %s\33[0m" % (response.status_code, response.text))


def request(server, uri_path, method, headers={}, body=None, token=None, verify_ssl=False, timeout=60):
    headers['Content-Type'] = 'application/json'
    if token != None:
        headers['Radar-Authentication'] = token
    if 'accept'.lower() not in [key.lower() for key in headers.keys()]:
        headers['accept'] = 'application/json'
    if 'X-API-Version'.lower() not in [key.lower() for key in headers.keys()]:
        headers['X-API-Version'] = '2.2'
    if body != None:
        if headers['Content-Type'] == 'application/json':
            body = json.dumps(body)
        # Otherwise, body is assumed to be a str
    logging.info('---BEGIN TSTT REQUEST---')
    logging.info('request is method={} headers={} body={}'.format(method, headers, body))
    # print(body)
    response = requests.request(url="https://{}{}".format(server, uri_path),
                                data=body,
                                method=method,
                                headers=headers,
                                verify=verify_ssl,
                                timeout=timeout)
    logging.info(
        'response is status={} body={} headers={}'.format(response.status_code, response.text, response.headers))
    logging.info('---END TSTT REQUEST---')
    logging.basicConfig(level=logging.INFO)
    return response


def radar_request(uri_path, method, headers={}, body=None, token=None, timeout=60):
    return request(radar_server, uri_path, method, headers, body, token, timeout=timeout)


def tstt_request(uri_path, method, headers={}, body=None, token=None, timeout=60):
    return request(tstt_server, uri_path, method, headers, body, token, timeout=timeout)


# def radar_get_request(uri_path, headers={}, token=None, timeout=60):
#     response = tstt_request(uri_path, 'GET', headers, None, token, timeout=timeout)
#     if response.status_code != 200:
#         raise_error(response)
#     return response.json()

def radar_get_request(uri_path, headers={}, token=None, timeout=60):
    response = radar_request(uri_path, 'GET', headers, None, token, timeout=timeout)
    if response.status_code != 200:
        raise_error(response)
    return response.json()


# def radar_post_request(uri_path, headers={}, body=None, token=None, timeout=60):
#     response = tstt_request(uri_path, 'POST', headers, body, token, timeout=timeout)
#     if response.status_code != 200:
#         raise_error(response)
#     return response

def radar_post_request(uri_path, headers={}, body=None, token=None, timeout=60):
    response = radar_request(uri_path, 'POST', headers, body, token, timeout=timeout)
    if response.status_code != 201:
        raise_error(response)
    return response

def radar_put_request(uri_path, headers={}, body=None, token=None, timeout=60):
    response = radar_request(uri_path, 'PUT', headers, body, token, timeout=timeout)
    if response.status_code != 205:
        raise_error(response)
    return response

def get_authentication_token():
    __, krb_context = kerberos.authGSSClientInit("HTTP@%s" % radar_server)
    kerberos.authGSSClientStep(krb_context, "")
    negotiate_details = kerberos.authGSSClientResponse(krb_context)
    headers = {"Authorization": "Negotiate " + negotiate_details}    
    response = radar_request("/signon", 'GET', headers=headers)
    response_json = response.json()
    if response.status_code == 200:
        return response_json['accessToken'], response_json['tokenExpiresIn']
    raise_error(response)


def find_resources(request_body, timeout, token, request_fields='all',headers={}):
    fields_for_request = ['resourceDefinitionId', 'category', 'class', 'component', 'createdAt', 'createdBy', 'lastModifiedAt', 'lastModifiedBy', 'originator', 'priority', 'title']
    if request_fields == 'all':
      request_fields = fields_for_request
    else:
      request_fields = set(request_fields)
    headers['X-Fields-Requested']= ','.join(request_fields)
    response = radar_request('/tests/resources/find', 'POST', body=request_body, token=token, timeout=timeout, headers=headers)
    if (response.status_code != 200):
        raise_error(response)
    return response.json()


def find_scheduled_resources(request_body, timeout, token):
    response = radar_request('/tests/scheduledresources/find', 'POST', body=request_body, token=token,
                            timeout=timeout)
    if (response.status_code != 200):
        raise_error(response)
    return response.json()


def find_resource_definitions(request_body, timeout, token, request_fields='all',headers={}):
    fields_for_request = ['resourceDefinitionId', 'category', 'class', 'component', 'createdAt', 'createdBy', 'lastModifiedAt', 'lastModifiedBy', 'originator', 'priority', 'title']
    if request_fields == 'all':
      request_fields = fields_for_request
    else:
      request_fields = set(request_fields)
    headers['X-Fields-Requested']= ','.join(request_fields)
    response = radar_request('/tests/resourcedefinitions/find', 'POST', body=request_body, token=token, headers=headers,timeout=timeout)
    if (response.status_code != 200):
        raise_error(response)
    return response.json()


def find_scheduled_resource_definitions(request_body, timeout, token):
    response = radar_request('/tests/scheduledresourcedefinitions/find', 'POST', body=request_body, token=token,
                            timeout=timeout)
    if (response.status_code != 200):
        raise_error(response)
    return response.json()


def resource_for_id(resource_id, timeout, token):
    return radar_get_request('/tests/resources/%i' % resource_id, token=token, timeout=timeout)


def create_or_update_resource_with_attachments(resource_body, attachments, timeout, token): # Pending update until radar test api support
    boundary = str(uuid.uuid1())
    delimiter = "\r\n--%s\r\n" % boundary
    close_delimiter = "\r\n--%s--\r\n" % boundary
    request_body = delimiter

    request_body += "Content-Disposition: form-data; name=request-body\r\n"
    request_body += "Content-Type: application/json; charset=utf-8\r\n\r\n"
    request_body += json.dumps(resource_body)

    for attachment in attachments:
        is_picture = attachment.content_type.startswith('image')
        request_body += delimiter
        request_body += "Content-Disposition: form-data; name=file; filename=\"%s\"; picture=%s\r\n" % (
            attachment.name, str(is_picture).lower())
        request_body += "Content-Type: %s\r\n\r\n" % attachment.content_type
        request_body += attachment.data

    request_body += close_delimiter

    headers = {'Content-Type': 'multipart/related; boundary=%s' % boundary}

    response = radar_request('/tests/resources/uploadFile', 'POST', headers=headers, body=request_body, token=token,
                            timeout=timeout)

    if (response.status_code != 200):
        raise_error(response)
    return response.json()

## decommissioned API 
# def keywords_for_resource_id(resource_id, timeout, token):
#     return radar_get_request('/tests/resources/%i' % resource_id, token=token, timeout=timeout)

## decommissioned API 
# def names_for_resource_id(resource_id, timeout, token):
#     return radar_get_request('/tests/resources/%i/name' % resource_id, token=token, timeout=timeout)


def update_name_of_resource(resource_id, name_id, name_title, name_description, name_type, token, timeout=30):
    body = {
            "names": {
            "insert": [
            {
                "id": name_id,
                "title": name_title,
                "typeId": name_type,
                "description": name_description
            }
            ]
        }
    }   
    return radar_put_request('/tests/resources/{}/name'.format(resource_id), body=body,
                             token=token, timeout=timeout)



def delete_name_of_resource(resource_id, name_id, timeout, token):
    body = {
        "names": {
            "remove": [
            name_id
            ]
        }
    }
    return radar_post_request(
        '/tests/resources/{}/name'.format(
            resource_id, name_id), body=body,
        token=token, timeout=timeout)

## decommissioned API 
# def numbers_for_resource_id(resource_id, timeout, token):
#     return radar_get_request('/tests/resources/%i/number' % resource_id, token=token, timeout=timeout)


def fields_for_resource_id(resource_id, request_fields, timeout, token, headers={}):
    fields_for_resource = ['configurationDescription','names','numbers','properties','keywords','relatedProblems','otherRelatedItems','securityList','discussion','relatedScheduledTestCases','label','protectionMaskList','attachments','pictures','ccList','counts','history']
    if request_fields == 'all':
      request_fields = fields_for_resource
    else:
      request_fields = set(request_fields)
    headers['X-Fields-Requested']= ','.join(request_fields)
    return radar_get_request('/tests/resources/%i' % resource_id, token=token, timeout=timeout, headers=headers)


# def related_problems_for_resource_id(resource_id, timeout, token):
#     headers = {'X-Fields-Requested': 'relatedProblems'}
#     return radar_get_request('/tests/resources/%i' % resource_id, token=token, timeout=timeout, headers=headers)


# def related_scheduled_test_cases_for_resource_id(resource_id, timeout, token):
#     headers = {'X-Fields-Requested': 'relatedScheduledTestCases'}
#     return radar_get_request('/tests/resources/%i' % resource_id, token=token, timeout=timeout, headers=headers)


# def other_related_items_for_resource_id(resource_id, timeout, token):
#     headers = {'X-Fields-Requested': 'otherRelatedItems'}
#     return radar_get_request('/tests/resources/%i' % resource_id, token=token, timeout=timeout, headers=headers)


# def notes_for_resource_id(resource_id, note_type, timeout, token):
#     headers = {'X-Fields-Requested': 'discussion'}
#     return radar_get_request('/tests/resources/%i' % resource_id, token=token, timeout=timeout, headers=headers)


# def notes_for_resource_definition_id(resource_id, note_type, timeout, token):
#     return radar_get_request('/tests/resource-definition/%i/notes/%i' % (resource_id, note_type), token=token,
#                             timeout=timeout)


def resource_definition_for_id(resource_definition_id, timeout, token):
    return radar_get_request('/tests/resourcedefinitions/%i' % resource_definition_id, token=token, timeout=timeout)


def fields_for_resource_definition_id(resource_definition_id, request_fields, timeout, token, headers={}):
    fields_for_resource_definition = ['discussion','keywords','securityList','relatedProblems','relatedScheduledTestCases','label','protectionMaskList','attachments','pictures','history','ccList','counts']
    if request_fields == 'all':
      request_fields = fields_for_resource_definition
    else:
      request_fields = set(request_fields)
    headers['X-Fields-Requested']= ','.join(request_fields)
    return radar_get_request('/tests/resourcedefinitions/%i' % resource_definition_id, headers=headers, token=token, timeout=timeout)


# def keywords_for_resource_definition_id(resource_definition_id, timeout, token):
#     return radar_get_request('/tests/resource-definition/%i/keywords' % resource_definition_id, token=token,
#                             timeout=timeout)


# def related_problems_for_resource_definition_id(resource_definition_id, timeout, token):
#     return radar_get_request('/tests/resource-definition/%i/related-problems' % resource_definition_id, token=token,
#                             timeout=timeout)


# def related_test_cases_for_resource_definition_id(resource_definition_id, timeout, token):
#     return radar_get_request('/tests/resource-definition/%i/related-test-cases' % resource_definition_id, token=token,
#                             timeout=timeout)


# def scheduled_resource_for_id(scheduled_resource_id, timeout, token):
#     return radar_get_request(
#         '/tests/scheduled-resources/%i?disclosureIdentifiers=&noteType=1' % scheduled_resource_id, token=token,
#         timeout=timeout)


# def names_for_scheduled_resource_id(scheduled_resource_id, timeout, token):
#     return radar_get_request('/tests/scheduled-resources/%i/name' % scheduled_resource_id, token=token,
#                             timeout=timeout)


# def numbers_for_scheduled_resource_id(scheduled_resource_id, timeout, token):
#     return radar_get_request('/tests/scheduled-resources/%i/number' % scheduled_resource_id, token=token,
#                             timeout=timeout)


# def properties_for_scheduled_resource_id(scheduled_resource_id, timeout, token):
#     return radar_get_request('/tests/scheduled-resources/%i/attribute-bundles' % scheduled_resource_id, token=token,
#                             timeout=timeout)


# def keywords_for_scheduled_resource_id(scheduled_resource_id, timeout, token):
#     return radar_get_request('/tests/scheduled-resources/%i/keywords' % scheduled_resource_id, token=token,
#                             timeout=timeout)


# def related_problems_for_scheduled_resource_id(scheduled_resource_id, timeout, token):
#     return radar_get_request('/tests/scheduled-resources/%i/related-problems' % scheduled_resource_id, token=token,
#                             timeout=timeout)


# def other_related_items_for_scheduled_resource_id(scheduled_resource_id, timeout, token):
#     return radar_get_request('/tests/scheduled-resources/%i/other-related-items' % scheduled_resource_id, token=token,
#                             timeout=timeout)


# def scheduled_resource_definition_for_id(scheduled_resource_definition_id, timeout, token):
#     return radar_get_request('/tests/scheduled-resource-definition/%i' % scheduled_resource_definition_id,
#                             token=token, timeout=timeout)


# def keywords_for_scheduled_resource_definition_id(scheduled_resource_definition_id, timeout, token):
#     return radar_get_request('/tests/scheduled-resource-definition/%i/keywords' % scheduled_resource_definition_id,
#                             token=token, timeout=timeout)


# def related_problems_for_scheduled_resource_definition_id(scheduled_resource_definition_id, timeout, token):
#     return radar_get_request(
#         '/tests/scheduled-resource-definition/%i/related-problems' % scheduled_resource_definition_id, token=token,
#         timeout=timeout)


# def related_test_cases_for_scheduled_resource_definition_id(scheduled_resource_definition_id, timeout, token):
#     return radar_get_request(
#         '/tests/scheduled-resource-definition/%i/related-test-cases' % scheduled_resource_definition_id, token=token,
#         timeout=timeout)


def resources_for_test_suite_id(test_suite_id, timeout, token):
    return radar_get_request('/tests/suite/test-suite/%i' % test_suite_id, token=token, timeout=timeout)


def resources_for_test_case_id(test_case_id, timeout, token):
    return radar_get_request('/tests/suite/test-suite-case/%i' % test_case_id, token=token, timeout=timeout)


def resources_for_scheduled_test_case_id(scheduled_test_id, timeout, token):
    return radar_get_request('/tests/scheduledtestsuitecase/getScheduledTestSuiteResourceList/%i' % scheduled_test_id,
                            token=token, timeout=timeout)


def resources_for_scheduled_test_id(scheduled_test_id, timeout, token):
    return radar_get_request('/tests/scheduledtestsuite/getScheduledTestResourceList/%i' % scheduled_test_id,
                            token=token, timeout=timeout)


def attach_res_to_scheduled_test(scheduled_test_id, res_ids, timeout, token):
    body = {
        "operation": 0,
        "resourceId": res_ids,
        "scheduledTestSuiteId": scheduled_test_id
    }
    return radar_post_request('/tests/scheduledtestsuite/addResourceAndResDefScheduledTestSuite', body=body,
                             token=token, timeout=timeout)


def attach_resdefs_to_scheduled_test(scheduled_test_id, resdef_ids, timeout, token):
    body = {
        "operation": 0,
        "resourceDefId": resdef_ids,
        "scheduledTestSuiteId": scheduled_test_id
    }
    return radar_post_request('/tests/scheduledtestsuite/addResourceAndResDefScheduledTestSuite', body=body,
                             token=token, timeout=timeout)


def remove_res_from_scheduled_test(scheduled_test_id, scheduled_res_ids, timeout, token):
    body = {
        "id": scheduled_res_ids,
        "isResource": 1
    }

    return radar_post_request(
        '/tests/scheduledtestsuite/{}/removeFromScheduledTestSuite'.format(scheduled_test_id),
        body=body,
        token=token, timeout=timeout)


def remove_resdef_from_scheduled_test(scheduled_test_id, scheduled_resdef_ids, timeout, token):
    body = {
        "id": scheduled_resdef_ids,
        "isResource": 0
    }

    return radar_post_request(
        '/tests/scheduledtestsuite/{}/removeFromScheduledTestSuite'.format(scheduled_test_id),
        body=body,
        token=token, timeout=timeout)


def attach_res_to_scheduled_test_case(scheduled_test_case_id, resdef_ids, timeout, token):
    body = {
        "operation": 0,
        "resourceId": resdef_ids,
        "scheduledTestCaseId": scheduled_test_case_id
    }
    return radar_post_request('/tests/scheduledtestsuite/addResourceAndResDefScheduledTestCase', body=body,
                             token=token, timeout=timeout)


def attach_resdefs_to_scheduled_test_case(scheduled_test_case_id, res_ids, timeout, token):
    body = {
        "operation": 0,
        "resourceDefId": res_ids,
        "scheduledTestCaseId": scheduled_test_case_id
    }
    return radar_post_request('/tests/scheduledtestsuite/addResourceAndResDefScheduledTestCase', body=body,
                             token=token, timeout=timeout)


def remove_res_from_scheduled_test_case(scheduled_test_case_id, scheduled_res_ids, timeout, token):
    body = {
        "id": scheduled_res_ids,
        "isResource": 1
    }

    return radar_post_request(
        '/tests/scheduledtestsuitecase/{}/removeFromScheduledTestSuiteCase'.format(scheduled_test_case_id),
        body=body,
        token=token, timeout=timeout)


def remove_resdefs_from_scheduled_test_case(scheduled_test_case_id, scheduled_resdef_ids, timeout, token):
    body = {
        "id": scheduled_resdef_ids,
        "isResource": 0
    }

    return radar_post_request(
        '/tests/scheduledtestsuitecase/{}/removeFromScheduledTestSuiteCase'.format(scheduled_test_case_id),
        body=body,
        token=token,
        timeout=timeout)


def set_focus_scheduled_res_to_scheduled_test(scheduled_test_id, focus_type, scheduled_res_ids, timeout, token):
    body = {
        "focusType": focus_type,
        "id": scheduled_res_ids,
        "isResource": 1,
        "isResourceOrDef": 1
    }
    return radar_post_request('/tests/scheduledtestsuite/{}/setFocusToScheduledTestSuite'.format(scheduled_test_id),
                             body=body,
                             token=token,
                             timeout=timeout)


def set_focus_scheduled_resdefs_to_scheduled_test(scheduled_test_id, focus_type, scheduled_res_def_ids, timeout, token):
    body = {
        "focusType": focus_type,
        "id": scheduled_res_def_ids,
        "isResource": 0,
        "isResourceOrDef": 1
    }
    return radar_post_request('/tests/scheduledtestsuite/{}/setFocusToScheduledTestSuite'.format(scheduled_test_id),
                             body=body,
                             token=token, timeout=timeout)


def create_resource(resource, timeout, token):
    return radar_post_request('/tests/resources', body=resource,
                             token=token, timeout=timeout)


def attach_keywords_to_resource(resource_id, keyword_ids, priority, timeout, token):
    body = {
        "id": keyword_ids,
        "lastModDate": datetime.datetime.utcnow().strftime("%Y-%m-%d %H:%M:%S.%f"),
        "operation": 0,
        "priority": priority
    }
    return radar_post_request('/tests/resources/{}/keywords'.format(resource_id), body=body,
                             token=token, timeout=timeout)


def attach_name_to_resource(resource_id, name_title, name_description, name_type, timeout, token):
    body = {
        "lastModDate": datetime.datetime.utcnow().strftime("%Y-%m-%d %H:%M:%S.%f"),
        "name": {
            "description": name_description,
            "title": name_title,
            "type": {
                "id": name_type
            }
        },
        "operation": 0
    }
    return radar_post_request('/tests/resources/{}/name'.format(resource_id), body=body,
                             token=token, timeout=timeout)


def attach_number_to_resource(resource_id, number_title, number_description, number_type, timeout, token):
    body = {
        "lastModDate": datetime.datetime.utcnow().strftime("%Y-%m-%d %H:%M:%S.%f"),
        "number": {
            "description": number_description,
            "title": number_title,
            "type": {
                "id": number_type
            }
        },
        "operation": 0
    }
    return radar_post_request('/tests/resources/{}/number'.format(resource_id), body=body,
                             token=token, timeout=timeout)


def update_number_of_resource(resource_id, number_id, number_title, number_description, number_type, timeout, token):
    body = {
        "number": {
            "numberId": number_id,
            "title": number_title,
            "type": {
                "id": number_type
            },
            "description": number_description
        },
        "operation": 1
    }
    return radar_post_request('/tests/resources/{}/number'.format(resource_id), body=body,
                             token=token, timeout=timeout)


def delete_number_of_resource(resource_id, number_id, timeout, token):
    body = {
        "operation": 2,
        "id": [
            number_id
        ],
        "lastModDate": datetime.datetime.utcnow().strftime("%Y-%m-%d %H:%M:%S.%f")
    }
    return radar_post_request(
        '/tests/resources/{}/number'.format(
            resource_id, number_id), body=body,
        token=token, timeout=timeout)


def update_resource(resource_id, resource, timeout, token):
    return radar_put_request('/tests/resources/{}'.format(resource_id), body=resource,
                             token=token, timeout=timeout)


def attach_property_to_resource(resource_id, item_id, item_name, title, description, type, timeout, token):
    body = {
        "itemName": item_name,
        "lastModDate": datetime.datetime.utcnow().strftime("%Y-%m-%d %H:%M:%S.%f"),
        "operation": 0,
        "title": title,
        "description": description,
        "type": {
            "id": type
        }
    }
    return radar_post_request(
        '/tests/resources/{}/attribute-bundles/{}/attributes'.format(
            resource_id, item_id), body=body,
        token=token, timeout=timeout)


def update_property_of_resource(resource_id, item_id, name_id, item_name, title, description, type, timeout, token):
    body = {
        "operation": 1,
        "id": name_id,
        "itemName": item_name,
        "title": title,
        "lastModDate": datetime.datetime.utcnow().strftime("%Y-%m-%d %H:%M:%S.%f"),
        "description": description,
        "type": {
            "id": type
        },
        "old": {
            "id": name_id
        },
    }
    return radar_post_request(
        '/tests/resources/{}/attribute-bundles/{}/attributes'.format(
            resource_id, item_id), body=body,
        token=token, timeout=timeout)


def delete_property_of_resource(resource_id, item_id, name_id, timeout, token):
    body = {
        "operation": 2,
        "id": [
            name_id
        ],
        "itemId": item_id,
        "lastModDate": datetime.datetime.utcnow().strftime("%Y-%m-%d %H:%M:%S.%f")
    }
    return radar_post_request(
        '/tests/resources/{}/attribute-bundles/{}/attributes'.format(
            resource_id, item_id), body=body,
        token=token, timeout=timeout)


def create_resource_definition(resource_definition, timeout, token):
    return radar_post_request('/tests/resource-definition', body=resource_definition,
                             token=token, timeout=timeout)


def update_resource_definition(resource_definition_id, resource_definition, timeout, token):
    return radar_put_request('/tests/resourcedefinitions/{}'.format(resource_definition_id),
                             body=resource_definition,
                             token=token, timeout=timeout)


def attach_keywords_to_resource_definition(resource_definition_id, keyword_ids, priority, timeout, token):
    body = {
        "id": keyword_ids,
        "lastModDate": datetime.datetime.utcnow().strftime("%Y-%m-%d %H:%M:%S.%f"),
        "operation": 0,
        "priority": priority
    }
    return radar_post_request('/tests/resource-definition/{}/keywords'.format(resource_definition_id), body=body,
                             token=token, timeout=timeout)
