Resource Manager Python - Installation Instructions
==================================================

SYSTEM REQUIREMENTS:
- macOS 10.14 (Mojave) or later
- Python 3.7 or later
- Internet connection for API operations
- Apple Connect credentials with Kerberos setup

INSTALLATION OPTIONS:

Option 1: Automated Installation (Recommended)
1. Open Terminal and navigate to the package directory
2. Run the installation script:
   ./scripts/install.sh
3. Follow the on-screen instructions

Option 2: Manual Installation
1. Create virtual environment:
   python3 -m venv venv
2. Activate virtual environment:
   source venv/bin/activate
3. Install dependencies:
   pip install -r requirements_macos.txt
4. Run the application:
   python main.py

AUTHENTICATION SETUP:
Before first use, set up Kerberos authentication:
1. Open Terminal
2. Run: kinit <EMAIL>
3. Enter your Apple Connect password when prompted

USAGE:
1. Launch the application
2. Use the tabs to navigate between different functions:
   - Create Resource: Create single or multiple resources
   - Update Resources: Update existing resources from CSV
   - Print Resources: Generate reports
   - Resource Count: View statistics

TROUBLESHOOTING:
- If authentication fails, refresh Kerberos tickets with kinit
- For GUI issues, ensure wxPython is properly installed
- Check ~/Documents/ResourceManager_app.log for error details
- Enable debug mode: DEBUG_LOGGING=1 python main.py

SUPPORT:
For internal Apple support, contact the development team.
