# Resource Manager - Development Guide

This guide provides detailed information for developers working on the Resource Manager Python application.

## 🏗️ Architecture Overview

The Resource Manager follows a clean architecture pattern with clear separation of concerns:

```
┌─────────────────────────────────────────────────────────────┐
│                    Presentation Layer                       │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │   Main Frame    │  │     Panels      │  │   Dialogs    │ │
│  │  (main_frame)   │  │ (create/update) │  │              │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                   Application Layer                         │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │            Application Service                          │ │
│  │         (application_service.py)                        │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                     Domain Layer                            │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │ Resource Manager│  │    Resource     │  │  Exceptions  │ │
│  │ (resource_mgr)  │  │  (Resource.py)  │  │              │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                 Infrastructure Layer                        │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │ Resource Client │  │ Data Access     │  │ File Manager │ │
│  │ (API Gateway)   │  │     Layer       │  │              │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## 📁 Module Structure

### Core Modules

#### `core/Resource.py`
- **Purpose**: Main business logic for resource operations
- **Key Classes**: `Resource`
- **Responsibilities**: 
  - Resource creation and updates
  - Data processing and validation
  - CSV file handling
  - Title and priority extraction

#### `core/resource_manager.py`
- **Purpose**: High-level resource management interface
- **Key Classes**: `ResourceManager`
- **Responsibilities**:
  - Orchestrates resource operations
  - Provides clean API for GUI layer
  - Error handling and validation

#### `core/resource_client.py`
- **Purpose**: API client for TSTT Resource API
- **Key Classes**: `TSTTResourceClient`
- **Responsibilities**:
  - HTTP API communication
  - Authentication token management
  - Request/response handling

#### `core/application_service.py`
- **Purpose**: Application service layer
- **Key Classes**: `ApplicationService`
- **Responsibilities**:
  - Coordinates between GUI and business logic
  - Observer pattern implementation
  - Standardized response handling

### GUI Modules

#### `gui/main_frame.py`
- **Purpose**: Main application window
- **Key Classes**: `MainFrame`
- **Responsibilities**:
  - Window management
  - Tab/panel coordination
  - Menu and toolbar handling

#### `gui/panels/`
- **Purpose**: Individual UI panels
- **Key Files**:
  - `create_panel.py`: Resource creation interface
  - `update_panel.py`: Resource update interface
  - `print_panel.py`: Print/export functionality
  - `resource_count_panel.py`: Statistics display

### Utility Modules

#### `utils/csv_handler.py`
- **Purpose**: CSV file processing
- **Key Classes**: `CSVHandler`
- **Responsibilities**:
  - CSV reading/writing
  - Data format conversion
  - Error handling for file operations

#### `utils/file_path_manager.py`
- **Purpose**: Centralized file path management
- **Key Classes**: `FilePathManager`
- **Responsibilities**:
  - Path resolution
  - Directory creation
  - File existence checking

## 🔧 Development Setup

### Prerequisites
- macOS 10.14 or later
- Python 3.7+
- Xcode Command Line Tools
- Access to Apple internal networks

### Environment Setup
```bash
# Clone the repository
git clone <repository-url>
cd resource_manager_macos

# Run the installation script
./install.sh

# Or manual setup:
python3 -m venv venv
source venv/bin/activate
pip install -r requirements_macos.txt
```

### Development Dependencies
```bash
# Install development tools
pip install pytest pytest-cov black flake8 mypy
```

## 🧪 Testing

### Running Tests
```bash
# Activate virtual environment
source venv/bin/activate

# Run all tests
python -m pytest tests/

# Run with coverage
python -m pytest --cov=core --cov=gui --cov=utils tests/

# Run specific test file
python -m pytest tests/test_resource_manager.py
```

### Manual Testing
```bash
# Run with debug logging
DEBUG_LOGGING=1 python main.py

# Test specific functionality
python -c "
from core.resource_manager import ResourceManager
rm = ResourceManager()
print('ResourceManager initialized successfully')
"
```

## 🏗️ Building

### Development Build
```bash
# Run directly
python main.py

# With debug output
DEBUG_LOGGING=1 python main.py
```

### Production Build
```bash
# Build standalone application
pyinstaller ResourceManager.spec

# The built app will be in dist/ResourceManager.app
```

### Distribution Package
```bash
# Create distribution package (if script exists)
./create_distribution.sh
```

## 🔍 Code Style

### Python Style Guide
- Follow PEP 8 conventions
- Use type hints where appropriate
- Document all public methods and classes
- Maximum line length: 100 characters

### Code Formatting
```bash
# Format code with black
black core/ gui/ utils/ main.py

# Check style with flake8
flake8 core/ gui/ utils/ main.py

# Type checking with mypy
mypy core/ gui/ utils/ main.py
```

### Documentation Standards
```python
def example_function(param1: str, param2: Optional[int] = None) -> bool:
    """
    Brief description of the function.
    
    Args:
        param1: Description of param1
        param2: Description of param2 (optional)
        
    Returns:
        Description of return value
        
    Raises:
        ValueError: When param1 is invalid
        ResourceManagerException: When operation fails
    """
    pass
```

## 🐛 Debugging

### Debug Logging
Enable debug logging by setting environment variable:
```bash
DEBUG_LOGGING=1 python main.py
```

### Common Debug Scenarios

#### API Issues
```python
# Test API connectivity
from core.resource_client import TSTTResourceClient
client = TSTTResourceClient()
try:
    # Test authentication
    client.verify_token()
    print("Authentication successful")
except Exception as e:
    print(f"Authentication failed: {e}")
```

#### GUI Issues
```python
# Test GUI components
import wx
app = wx.App()
frame = wx.Frame(None, title="Test")
frame.Show()
app.MainLoop()
```

#### File Operations
```python
# Test file operations
from utils.file_path_manager import file_path_manager
from utils.csv_handler import CSVHandler

# Test CSV reading
data = CSVHandler.read_csv("test_file.csv")
print(f"Read {len(data)} rows")
```

## 🔄 Contributing

### Development Workflow
1. Create feature branch from main
2. Implement changes with tests
3. Run code quality checks
4. Submit pull request
5. Code review and merge

### Code Quality Checklist
- [ ] All tests pass
- [ ] Code follows style guidelines
- [ ] Type hints are present
- [ ] Documentation is updated
- [ ] No debug print statements
- [ ] Error handling is appropriate

### Commit Message Format
```
type(scope): brief description

Detailed description if needed

- List specific changes
- Reference issue numbers if applicable
```

Types: feat, fix, docs, style, refactor, test, chore

## 📊 Performance Considerations

### API Rate Limiting
- Implement request throttling
- Use connection pooling
- Handle timeout gracefully

### Memory Management
- Close file handles properly
- Avoid loading large CSV files entirely into memory
- Use generators for large data processing

### GUI Responsiveness
- Use threading for long-running operations
- Implement progress indicators
- Use wx.CallAfter for thread-safe GUI updates

## 🔒 Security Considerations

### Authentication
- Never store credentials in code
- Use Kerberos for secure authentication
- Implement proper token refresh logic

### Data Handling
- Validate all user inputs
- Sanitize file paths
- Handle sensitive data appropriately

### API Security
- Use HTTPS for all API calls
- Validate SSL certificates
- Implement proper error handling without exposing internals

## 📝 Release Process

### Version Management
- Use semantic versioning (MAJOR.MINOR.PATCH)
- Update version in setup.py and config.py
- Tag releases in version control

### Release Checklist
- [ ] All tests pass
- [ ] Documentation is updated
- [ ] Version numbers are updated
- [ ] Build process works correctly
- [ ] Distribution package is created
- [ ] Release notes are prepared

### Deployment
1. Build production application
2. Test on clean macOS system
3. Create distribution package
4. Update documentation
5. Notify users of new release
