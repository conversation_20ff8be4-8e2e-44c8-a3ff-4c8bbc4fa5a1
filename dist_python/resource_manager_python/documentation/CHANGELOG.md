# Changelog

## [1.0.0] - 2024-01-01

### Added
- Initial release of Resource Manager Python application
- Resource creation and update functionality
- CSV import/export capabilities
- GUI interface with wxPython
- API integration with TSTT Resource API
- Kerberos authentication support
- Comprehensive error handling
- File path management utilities
- Print/export functionality

### Features
- Single and batch resource creation
- Resource updates from CSV files
- Template generation for CSV formats
- Real-time status updates
- Hyperlink generation for created resources
- Debug logging capabilities
- Cross-platform compatibility (macOS focused)

### Technical
- Clean architecture with dependency injection
- Standardized API response handling
- Comprehensive test coverage
- PyInstaller build support
- Distribution package creation
