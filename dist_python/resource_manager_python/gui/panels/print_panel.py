import wx
import wx.adv
import subprocess
import platform
from pathlib import Path

class PrintPanel(wx.Panel):
    def __init__(self, parent, app_service):
        super().__init__(parent)
        self.app_service = app_service
        self.result_hyperlink = None

        sizer = wx.BoxSizer(wx.VERTICAL)
        sizer.Add(wx.StaticText(self, label="Print Resources"), flag=wx.ALL, border=5)

        # Print Resources Button
        print_button = wx.Button(self, label="Print Resources")
        print_button.Bind(wx.EVT_BUTTON, self.on_print_resources)
        sizer.Add(print_button, 0, wx.ALL, 10)

        # Result display area
        self.result_sizer = wx.BoxSizer(wx.VERTICAL)
        sizer.Add(self.result_sizer, 0, wx.ALL | wx.EXPAND, 10)

        self.SetSizer(sizer)

    def on_print_resources(self, event):
        try:
            response = self.app_service.print_resources()
            if response.is_success():
                self.show_print_result(response.data)
            else:
                wx.MessageBox(response.message, "Print Error")
        except Exception as e:
            wx.MessageBox(str(e), "Print Error")

    def show_print_result(self, output_path):
        """Display the print result with clickable file path."""
        # Clear previous results
        self.clear_result_display()

        # Extract filename from path
        file_path = Path(output_path)
        filename = file_path.name

        # Create success message
        success_text = wx.StaticText(self, label="✅ Print completed successfully!")
        success_text.SetForegroundColour(wx.Colour(0, 128, 0))  # Green color
        self.result_sizer.Add(success_text, 0, wx.ALL, 5)

        # Create file info
        file_info_text = wx.StaticText(self, label=f"Output file: {filename}")
        file_info_text.SetFont(file_info_text.GetFont().Bold())
        self.result_sizer.Add(file_info_text, 0, wx.ALL, 5)

        # Create clickable hyperlink for file path
        self.result_hyperlink = wx.adv.HyperlinkCtrl(
            self,
            label=f"📁 {output_path}",
            url=""  # We'll handle the click manually
        )
        self.result_hyperlink.Bind(wx.adv.EVT_HYPERLINK, lambda evt: self.open_file_location(output_path))
        self.result_hyperlink.SetToolTip(wx.ToolTip("Click to open file location"))
        self.result_sizer.Add(self.result_hyperlink, 0, wx.ALL, 5)

        # Refresh layout
        self.Layout()

    def clear_result_display(self):
        """Clear previous result display."""
        # Remove all children from result_sizer
        for child in self.result_sizer.GetChildren():
            child.GetWindow().Destroy()
        self.result_sizer.Clear()

    def open_file_location(self, file_path):
        """Open the file location in the system file manager."""
        try:
            file_path = Path(file_path)

            # Get the directory containing the file
            directory = file_path.parent

            system = platform.system()
            if system == "Darwin":  # macOS
                subprocess.run(["open", "-R", str(file_path)], check=True)
            elif system == "Windows":
                subprocess.run(["explorer", "/select,", str(file_path)], check=True)
            else:  # Linux and others
                # Try to open the directory
                subprocess.run(["xdg-open", str(directory)], check=True)

        except Exception as e:
            wx.MessageBox(f"Could not open file location: {str(e)}", "Error")
