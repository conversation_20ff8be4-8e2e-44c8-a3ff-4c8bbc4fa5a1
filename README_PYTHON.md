# Resource Manager - Python Version

A comprehensive macOS application for managing Apple internal resources through the TSTT Resource API. This Python version provides a complete GUI interface built with wxPython for resource creation, updating, and management operations.

## 📋 Table of Contents

- [Features](#features)
- [System Requirements](#system-requirements)
- [Installation](#installation)
- [Project Structure](#project-structure)
- [Dependencies](#dependencies)
- [Configuration](#configuration)
- [Usage](#usage)
- [Development](#development)
- [Building](#building)
- [API Integration](#api-integration)
- [Troubleshooting](#troubleshooting)

## ✨ Features

### Core Functionality
- **Resource Creation**: Create single or multiple resources from CSV files
- **Resource Updates**: Update existing resources with new information
- **Print Resources**: Generate CSV reports for created/updated resources
- **Resource Count**: Track and display resource statistics
- **CSV Template Generation**: Download templates for proper CSV formatting

### Technical Features
- **GUI Interface**: Native macOS application using wxPython
- **API Integration**: Direct integration with Apple's TSTT Resource API
- **Kerberos Authentication**: Secure authentication using Apple Connect credentials
- **Error Handling**: Comprehensive error handling and user feedback
- **File Management**: Centralized file path management and CSV processing
- **Dependency Injection**: Clean architecture with dependency injection
- **Response Models**: Standardized API response handling

## 🖥️ System Requirements

- **macOS**: 10.14 (Mojave) or later
- **Python**: 3.7 or later
- **Architecture**: Intel x86_64 or Apple Silicon (ARM64)
- **Network**: Access to Apple internal networks and APIs
- **Authentication**: Valid Apple Connect credentials with Kerberos setup

## 📦 Installation

### Option 1: Pre-built Application
1. Download the latest `.app` bundle from the distribution package
2. Move `ResourceManager.app` to your `/Applications` folder
3. Right-click and select "Open" to bypass Gatekeeper (first launch only)
4. Grant necessary permissions when prompted

### Option 2: Development Setup
```bash
# Clone the repository
git clone <repository-url>
cd resource_manager_macos

# Create virtual environment
python3 -m venv venv
source venv/bin/activate

# Install dependencies
pip install -r requirements_macos.txt

# Run the application
python main.py
```

## 📁 Project Structure

```
resource_manager_macos/
├── main.py                     # Application entry point
├── config.py                   # Configuration settings
├── requirements_macos.txt      # Python dependencies
├── ResourceManager.spec        # PyInstaller build specification
├── core/                       # Core business logic
│   ├── __init__.py
│   ├── Resource.py             # Main resource operations
│   ├── resource_manager.py     # Resource management service
│   ├── resource_client.py      # API client wrapper
│   ├── resource_core.py        # Core API functions
│   ├── application_service.py  # Application service layer
│   ├── data_access_layer.py    # Data access abstraction
│   ├── interfaces.py           # Interface definitions
│   ├── exceptions.py           # Custom exceptions
│   ├── response_models.py      # Response data models
│   ├── dto.py                  # Data transfer objects
│   └── res2resdef.py          # Resource-to-definition mapping
├── gui/                        # User interface components
│   ├── __init__.py
│   ├── main_frame.py          # Main application window
│   └── panels/                # UI panels
│       ├── create_panel.py    # Resource creation panel
│       ├── update_panel.py    # Resource update panel
│       ├── print_panel.py     # Print resources panel
│       └── resource_count_panel.py # Resource count panel
├── utils/                      # Utility modules
│   ├── __init__.py
│   ├── csv_handler.py         # CSV processing utilities
│   ├── file_path_manager.py   # File path management
│   └── output_redirector.py   # Output redirection for GUI
├── templates/                  # CSV templates
├── assets/                     # Application assets
└── dist/                      # Built applications
```

## 🔧 Dependencies

### Core Dependencies
- **wxPython** (>=4.1.0): Cross-platform GUI toolkit
- **requests** (>=2.25.0): HTTP library for API calls
- **urllib3** (>=1.26.0): HTTP client library
- **pykerberos** (>=1.2.1): Kerberos authentication support
- **pyinstaller** (>=5.0.0): Application packaging

### Standard Library Modules
- pathlib, csv, json, re, datetime, logging, threading, queue
- os, sys, tempfile, platform, uuid, collections
- abc, enum, dataclasses, typing

## ⚙️ Configuration

The application uses a centralized configuration system in `config.py`:

```python
class Config:
    # API Configuration
    API_TIMEOUT = 60
    ENV_PRODUCTION = "PRODUCTION"
    ENV_UAT = "UAT"
    
    # Default Resource Values
    DEFAULT_COMPONENT_ID = 1118899
    DEFAULT_DRI_ID = 973776146
    DEFAULT_LOCATION_ID = 66842
    DEFAULT_SPECIFIC_LOCATION = "Aruba"
    
    # Priority Configuration
    PRIORITY_MIN = 0
    PRIORITY_MAX = 5
    PRIORITY_DEFAULT = 5
```

## 🚀 Usage

### Authentication Setup
Before using the application, ensure Kerberos authentication is configured:

```bash
# Obtain Kerberos tickets
kinit <EMAIL>
```

### Creating Resources
1. Launch the application
2. Navigate to the "Create Resource" tab
3. Either:
   - Fill in the form fields for single resource creation
   - Upload a CSV file for batch creation
4. Click "Create Resource" or "Create from CSV File"

### Updating Resources
1. Navigate to the "Update Resources" tab
2. Download the CSV template if needed
3. Prepare your CSV file with resource IDs and update data
4. Select your CSV file and click "Update"

### CSV File Formats

#### Resource Creation CSV
```csv
Title*,Label Title,Qty,speed,resdef*,Pri,resourceID*,res_link,resdef_link,location,category_id
"Test Resource",Test,1,USB3_5,12345,2,,,,Aruba,
```

#### Resource Update CSV
```csv
Title*,Label Title,Qty,speed,resdef*,Pri,resourceID*,res_link,resdef_link,location
"Updated Title",,1,USB3_5,12345,3,98765,rdar://res/98765,rdar://resdef/12345,Aruba
```

## 🛠️ Development

### Setting Up Development Environment
```bash
# Install development dependencies
pip install -r requirements_macos.txt

# Run in development mode
python main.py

# Run with debug logging
DEBUG_LOGGING=1 python main.py
```

### Code Architecture
The application follows clean architecture principles:

- **Presentation Layer**: GUI components in `gui/`
- **Application Layer**: Service coordination in `core/application_service.py`
- **Domain Layer**: Business logic in `core/resource_manager.py` and `core/Resource.py`
- **Infrastructure Layer**: API clients and data access in `core/resource_client.py`

### Testing
```bash
# Run unit tests (if available)
python -m pytest tests/

# Manual testing with debug output
DEBUG_LOGGING=1 python main.py
```

## 📦 Building

### Building Standalone Application
```bash
# Build using PyInstaller
pyinstaller ResourceManager.spec

# The built application will be in dist/ResourceManager.app
```

### Distribution Package Creation
```bash
# Create distribution package (if script exists)
./create_distribution.sh
```

## 🔌 API Integration

The application integrates with Apple's internal TSTT Resource API:

- **Authentication**: Kerberos-based authentication
- **Endpoints**: Resource creation, updates, queries, and definitions
- **Error Handling**: Comprehensive API error handling and retry logic
- **Rate Limiting**: Built-in request throttling and timeout handling

### API Client Usage
```python
from core.resource_client import TSTTResourceClient

client = TSTTResourceClient()
# Client automatically handles authentication and token refresh
```

## 🔧 Troubleshooting

### Common Issues

#### Authentication Errors
```bash
# Refresh Kerberos tickets
kinit <EMAIL>

# Verify ticket status
klist
```

#### GUI Issues
- Ensure wxPython is properly installed for your Python version
- On macOS, you may need to install Python from python.org rather than Homebrew

#### API Connection Issues
- Verify network connectivity to Apple internal services
- Check firewall settings and VPN connection
- Ensure proper DNS resolution for API endpoints

#### File Permission Issues
- Grant necessary file system permissions when prompted
- Ensure write access to Downloads folder for output files

### Debug Mode
Enable debug logging by setting the environment variable:
```bash
DEBUG_LOGGING=1 python main.py
```

### Log Files
Application logs are written to:
- `~/Documents/ResourceManager_app.log`

## 📄 License

This software is proprietary to Apple Inc. and is intended for internal use only.

## 🤝 Support

For support and questions:
1. Check the troubleshooting section above
2. Review application logs for error details
3. Contact the development team for internal Apple support
