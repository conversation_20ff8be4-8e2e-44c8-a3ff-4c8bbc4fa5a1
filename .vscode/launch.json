{"configurations": [{"type": "swift", "request": "launch", "args": [], "cwd": "${workspaceFolder:resource_manager_macos}/ResourceManagerSwift", "name": "Debug ResourceManagerApp (ResourceManagerSwift)", "program": "${workspaceFolder:resource_manager_macos}/ResourceManagerSwift/.build/debug/ResourceManagerApp", "preLaunchTask": "swift: Build Debug ResourceManagerApp (ResourceManagerSwift)"}, {"type": "swift", "request": "launch", "args": [], "cwd": "${workspaceFolder:resource_manager_macos}/ResourceManagerSwift", "name": "Release ResourceManagerApp (ResourceManagerSwift)", "program": "${workspaceFolder:resource_manager_macos}/ResourceManagerSwift/.build/release/ResourceManagerApp", "preLaunchTask": "swift: Build Release ResourceManagerApp (ResourceManagerSwift)"}]}