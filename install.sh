#!/bin/bash

# Resource Manager Python Installation Script
# This script sets up the Resource Manager Python application on macOS

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running on macOS
check_macos() {
    if [[ "$OSTYPE" != "darwin"* ]]; then
        print_error "This script is designed for macOS only."
        exit 1
    fi
    print_success "Running on macOS"
}

# Check Python version
check_python() {
    if ! command -v python3 &> /dev/null; then
        print_error "Python 3 is not installed. Please install Python 3.7 or later."
        print_status "You can download Python from: https://www.python.org/downloads/"
        exit 1
    fi
    
    PYTHON_VERSION=$(python3 -c 'import sys; print(".".join(map(str, sys.version_info[:2])))')
    REQUIRED_VERSION="3.7"
    
    if ! python3 -c "import sys; exit(0 if sys.version_info >= (3, 7) else 1)"; then
        print_error "Python $PYTHON_VERSION is installed, but Python $REQUIRED_VERSION or later is required."
        exit 1
    fi
    
    print_success "Python $PYTHON_VERSION is installed"
}

# Check if pip is available
check_pip() {
    if ! command -v pip3 &> /dev/null; then
        print_error "pip3 is not available. Please install pip."
        exit 1
    fi
    print_success "pip3 is available"
}

# Create virtual environment
create_venv() {
    print_status "Creating virtual environment..."
    
    if [ -d "venv" ]; then
        print_warning "Virtual environment already exists. Removing old one..."
        rm -rf venv
    fi
    
    python3 -m venv venv
    print_success "Virtual environment created"
}

# Activate virtual environment and install dependencies
install_dependencies() {
    print_status "Activating virtual environment and installing dependencies..."
    
    source venv/bin/activate
    
    # Upgrade pip
    pip install --upgrade pip
    
    # Install dependencies
    if [ -f "requirements_macos.txt" ]; then
        print_status "Installing dependencies from requirements_macos.txt..."
        pip install -r requirements_macos.txt
        print_success "Dependencies installed successfully"
    else
        print_error "requirements_macos.txt not found!"
        exit 1
    fi
}

# Check Kerberos setup
check_kerberos() {
    print_status "Checking Kerberos setup..."
    
    if ! command -v kinit &> /dev/null; then
        print_warning "kinit command not found. Kerberos may not be properly configured."
        print_status "This is required for Apple internal API authentication."
    else
        print_success "Kerberos tools are available"
        print_status "Remember to run 'kinit <EMAIL>' before using the application"
    fi
}

# Create desktop shortcut
create_shortcut() {
    print_status "Creating desktop shortcut..."
    
    SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
    SHORTCUT_PATH="$HOME/Desktop/Resource Manager.command"
    
    cat > "$SHORTCUT_PATH" << EOF
#!/bin/bash
cd "$SCRIPT_DIR"
source venv/bin/activate
python main.py
EOF
    
    chmod +x "$SHORTCUT_PATH"
    print_success "Desktop shortcut created: $SHORTCUT_PATH"
}

# Create launch script
create_launch_script() {
    print_status "Creating launch script..."
    
    cat > "run_resource_manager.sh" << 'EOF'
#!/bin/bash

# Resource Manager Launch Script
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# Check if virtual environment exists
if [ ! -d "venv" ]; then
    echo "Virtual environment not found. Please run install.sh first."
    exit 1
fi

# Activate virtual environment
source venv/bin/activate

# Check Kerberos authentication
if ! klist -s 2>/dev/null; then
    echo "Kerberos tickets not found or expired."
    echo "Please run: kinit <EMAIL>"
    read -p "Press Enter to continue anyway, or Ctrl+C to exit..."
fi

# Launch the application
echo "Starting Resource Manager..."
python main.py
EOF
    
    chmod +x "run_resource_manager.sh"
    print_success "Launch script created: run_resource_manager.sh"
}

# Test installation
test_installation() {
    print_status "Testing installation..."
    
    source venv/bin/activate
    
    # Test Python imports
    python3 -c "
import wx
import requests
import core.resource_manager
import gui.main_frame
print('All imports successful')
" 2>/dev/null
    
    if [ $? -eq 0 ]; then
        print_success "Installation test passed"
    else
        print_error "Installation test failed. Some dependencies may be missing."
        exit 1
    fi
}

# Main installation process
main() {
    echo "=========================================="
    echo "Resource Manager Python Installation"
    echo "=========================================="
    echo
    
    check_macos
    check_python
    check_pip
    create_venv
    install_dependencies
    check_kerberos
    create_shortcut
    create_launch_script
    test_installation
    
    echo
    echo "=========================================="
    print_success "Installation completed successfully!"
    echo "=========================================="
    echo
    print_status "To run the application:"
    echo "  1. Double-click 'Resource Manager.command' on your desktop, or"
    echo "  2. Run: ./run_resource_manager.sh"
    echo
    print_status "Before first use, authenticate with Kerberos:"
    echo "  kinit <EMAIL>"
    echo
    print_status "For development, activate the virtual environment:"
    echo "  source venv/bin/activate"
    echo "  python main.py"
    echo
}

# Run main function
main "$@"
