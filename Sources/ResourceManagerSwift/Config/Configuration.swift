import Foundation

/// Central configuration class for the Resource Manager application
class Configuration {
    
    // MARK: - Singleton
    static let shared = Configuration()
    private init() {}
    
    // MARK: - API Configuration
    struct API {
        static let baseURL = "https://api.example.com"  // Replace with actual API URL
        static let timeout: TimeInterval = 60
        static let version = "2.2"
    }
    
    // MARK: - Environment Configuration
    enum Environment: String, CaseIterable {
        case production = "production"
        case uat = "uat"
        case development = "development"
    }
    
    var currentEnvironment: Environment {
        if let envString = ProcessInfo.processInfo.environment["RESOURCE_MANAGER_ENV"],
           let env = Environment(rawValue: envString) {
            return env
        }
        return .production
    }
    
    // MARK: - File Paths Configuration
    var projectRoot: URL {
        return FileManager.default.homeDirectoryForCurrentUser
            .appendingPathComponent("Documents")
            .appendingPathComponent("ResourceManagerSwift")
    }
    
    var coreDirectory: URL {
        return projectRoot.appendingPathComponent("Core")
    }
    
    var outputDirectory: URL {
        return FileManager.default.homeDirectoryForCurrentUser
            .appendingPathComponent("Downloads")
    }
    
    // MARK: - CSV File Names
    struct CSVFiles {
        static let resourceCreate = "resource_create.csv"
        static let resourceUpdate = "resource_update.csv"
        static let resourceDup = "resource_dup.csv"
        static let resourceUpdateTemplate = "resource_update_template.csv"
    }
    
    // MARK: - Default Resource Values
    struct Defaults {
        static let componentId = 1118899
        static let driId = 973776146
        static let inventoryKeeperId = 973776146
        static let classId = 1
        static let stateId = 1
        static let locationId = 66842
        static let specificLocation = "Aruba"
        static let assigneeId = 1555131384
    }
    
    // MARK: - Priority Configuration
    struct Priority {
        static let min = 0
        static let max = 5
        static let defaultValue = 5
        static let naValue = 5
    }
    
    // MARK: - GUI Configuration
    struct GUI {
        static let mainWindowWidth: CGFloat = 800
        static let mainWindowHeight: CGFloat = 700
        static let outputTextMinHeight: CGFloat = 200
    }
    
    // MARK: - Logging Configuration
    struct Logging {
        static let level = "DEBUG"
        static let format = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
        static let fileName = "ResourceManager.log"
    }
    
    // MARK: - Helper Methods
    func ensureDirectoriesExist() {
        let directories = [coreDirectory, outputDirectory]
        
        for directory in directories {
            do {
                try FileManager.default.createDirectory(
                    at: directory,
                    withIntermediateDirectories: true,
                    attributes: nil
                )
            } catch {
                print("Failed to create directory: \(directory.path) - \(error)")
            }
        }
    }
    
    func getCSVFilePath(_ filename: String) -> URL {
        return coreDirectory.appendingPathComponent(filename)
    }
    
    func getOutputFilePath(_ filename: String) -> URL {
        ensureDirectoriesExist()
        return outputDirectory.appendingPathComponent(filename)
    }
    
    func getTimestampedFilename(baseName: String, extension: String = "csv") -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyyMMdd_HHmmss"
        let timestamp = formatter.string(from: Date())
        return "\(baseName)_\(timestamp).\(`extension`)"
    }
}

// MARK: - Security Configuration
extension Configuration {
    struct Security {
        static let verifySSL = false  // Set to true in production
        static let tokenRefreshThreshold: TimeInterval = 300  // 5 minutes
    }
}

// MARK: - Feature Flags
extension Configuration {
    struct FeatureFlags {
        static let enableLogging = true
        static let enableDebugMode = false
        static let enableAutoBackup = true
        static let enableProgressTracking = true
    }
}
