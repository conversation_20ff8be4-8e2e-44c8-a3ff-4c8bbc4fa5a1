<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Resources/base_library.zip</key>
		<data>
		vvQQ9LgjkmAiKvqf7dLH0BG7cfo=
		</data>
		<key>Resources/certifi/cacert.pem</key>
		<data>
		7cYViWSsxZme1UE1dd2aZQprzbI=
		</data>
		<key>Resources/certifi/py.typed</key>
		<data>
		2jmj7l5rSw0yVb/vlWAYkK/YBwk=
		</data>
		<key>Resources/config.py</key>
		<data>
		g6y3e7Vf1OyRIRjusihS1+/00Z0=
		</data>
		<key>Resources/core/Resource.py</key>
		<data>
		xMBk+9AK7s1nz4DiRDfXcxbE87k=
		</data>
		<key>Resources/core/__init__.py</key>
		<data>
		SsI6cc06lch1KnDdLCFhxDgnEUg=
		</data>
		<key>Resources/core/__pycache__/Resource.cpython-311.pyc</key>
		<data>
		8lvM1S0UjmmarPX83Vx2AFJrkeo=
		</data>
		<key>Resources/core/__pycache__/__init__.cpython-311.pyc</key>
		<data>
		3MEaNBNB9aBZGB1WHOMNyTBuAmk=
		</data>
		<key>Resources/core/__pycache__/__init__.cpython-39.pyc</key>
		<data>
		/RbbTR5xA9v6kMOP/85iGUMNKxU=
		</data>
		<key>Resources/core/__pycache__/application_service.cpython-311.pyc</key>
		<data>
		TicWKDH/xGvUoYrIzXXEV80Qcxg=
		</data>
		<key>Resources/core/__pycache__/application_service.cpython-39.pyc</key>
		<data>
		kHeVcQ0aYxUhCuqjG9QELrfQq2Y=
		</data>
		<key>Resources/core/__pycache__/data_access_layer.cpython-311.pyc</key>
		<data>
		m3fTMatrnyiaSZmKlrFTY/K6s2o=
		</data>
		<key>Resources/core/__pycache__/exceptions.cpython-311.pyc</key>
		<data>
		3uQkoDnpnWhStdi5M1/OLcl6XPA=
		</data>
		<key>Resources/core/__pycache__/exceptions.cpython-39.pyc</key>
		<data>
		helSmS7O3+JmD+AKTbu9Ge5Ws4M=
		</data>
		<key>Resources/core/__pycache__/interfaces.cpython-311.pyc</key>
		<data>
		Z3apX8TUSIvDgNffRld5F2ZLizM=
		</data>
		<key>Resources/core/__pycache__/interfaces.cpython-39.pyc</key>
		<data>
		I/qIqhtiaVfgPpT0q8l2Wt8IsiE=
		</data>
		<key>Resources/core/__pycache__/res2resdef.cpython-311.pyc</key>
		<data>
		tCySpN3/VtbJg39MXbc4uy9WPEs=
		</data>
		<key>Resources/core/__pycache__/resource_client.cpython-311.pyc</key>
		<data>
		XBpbC1ZX8MFeFhyH5XaTkwFO7cM=
		</data>
		<key>Resources/core/__pycache__/resource_client.cpython-39.pyc</key>
		<data>
		Zlpg0fxtyYJnCiNeVK0LBOMJt0I=
		</data>
		<key>Resources/core/__pycache__/resource_core.cpython-311.pyc</key>
		<data>
		AhdssS8ir/CWv3bbtwni4KLDPdw=
		</data>
		<key>Resources/core/__pycache__/resource_manager.cpython-311.pyc</key>
		<data>
		WTyQdrg+wbPtcOsT8MnSW5av5WA=
		</data>
		<key>Resources/core/__pycache__/resource_manager.cpython-39.pyc</key>
		<data>
		bgDRlNGtz3/9RJTKRK3y/M+wfko=
		</data>
		<key>Resources/core/__pycache__/response_models.cpython-311.pyc</key>
		<data>
		AbTkQ3yWeF1U7+4GTcpSTLMH2Rk=
		</data>
		<key>Resources/core/__pycache__/response_models.cpython-39.pyc</key>
		<data>
		i0BlN/0fQIpN1YQ0cTGONoGwJ8w=
		</data>
		<key>Resources/core/application_service.py</key>
		<data>
		ljUnD3NoT42G3C+jPaZlq3CvIX0=
		</data>
		<key>Resources/core/data_access_layer.py</key>
		<data>
		g+enAAXgnmjpYngozNzHympxXW4=
		</data>
		<key>Resources/core/data_access_layer.py.backup</key>
		<data>
		X/PdrDGZ27ku756JyilwVIOwugA=
		</data>
		<key>Resources/core/data_access_layer_broken.py</key>
		<data>
		FIisdcLD7Q7Xd47e7AUi3TIQGqs=
		</data>
		<key>Resources/core/dto.py</key>
		<data>
		xjugv/O7nra3ybKPQgglpEBH06w=
		</data>
		<key>Resources/core/exceptions.py</key>
		<data>
		JeEqQcaH+K+vdysGZo7I4HhZ4GM=
		</data>
		<key>Resources/core/interfaces.py</key>
		<data>
		bzPlm4ZaiNigJjnNl8Fv3uD1wj0=
		</data>
		<key>Resources/core/res2resdef.py</key>
		<data>
		fudMVlzgtMzEOp47eWlJ70lt3Es=
		</data>
		<key>Resources/core/resource_client.py</key>
		<data>
		OTf5NXbuL3SYuuUf5lsD2lVuLm8=
		</data>
		<key>Resources/core/resource_core.py</key>
		<data>
		fPIhEqTGL2Hp6aGdiCD4A4yrD/I=
		</data>
		<key>Resources/core/resource_create.csv</key>
		<data>
		WXxDqWJPGJKHtbMS2yJ3vIDcVXw=
		</data>
		<key>Resources/core/resource_dup.csv</key>
		<data>
		pJmJXTgDIu+7MIVSRp3lrZxOEXo=
		</data>
		<key>Resources/core/resource_manager.py</key>
		<data>
		wDSy0Cw3T4HgmqwgfX49nWbFMtI=
		</data>
		<key>Resources/core/resource_manager.py.backup</key>
		<data>
		Wh2ASz/CjMxzslJbmo9RECx5h0M=
		</data>
		<key>Resources/core/resource_update_template.csv</key>
		<data>
		E+vvzUkkjPbww+CCJVxX0932Izo=
		</data>
		<key>Resources/core/response_models.py</key>
		<data>
		ZOjTHeSJKVl/VAlhgM0UGrYJ8bc=
		</data>
		<key>Resources/gui/__init__.py</key>
		<data>
		klYvFSil4+XnkQdeurbMNQLSpPM=
		</data>
		<key>Resources/gui/__pycache__/__init__.cpython-311.pyc</key>
		<data>
		KtCd8MC4ERtShlitVrTBc0jU+AQ=
		</data>
		<key>Resources/gui/__pycache__/main_frame.cpython-311.pyc</key>
		<data>
		2ev3ZS4e4IcbbXUH65mbHgGcXyw=
		</data>
		<key>Resources/gui/main_frame.py</key>
		<data>
		2NYn389Teyg1dnbOG604ArEU+dg=
		</data>
		<key>Resources/gui/panels/__pycache__/create_panel.cpython-311.pyc</key>
		<data>
		qDPQsg1m53vyM/pYKBqqc+7iGAY=
		</data>
		<key>Resources/gui/panels/__pycache__/print_panel.cpython-311.pyc</key>
		<data>
		sDnVRofwPf6ejlI8S47KNTceKTM=
		</data>
		<key>Resources/gui/panels/__pycache__/resource_count_panel.cpython-311.pyc</key>
		<data>
		vd3N1mErovjUj5/60LO3ofQzZIM=
		</data>
		<key>Resources/gui/panels/__pycache__/update_panel.cpython-311.pyc</key>
		<data>
		Bi+ZmdDXNdyZVOjCetgvu5pBeNA=
		</data>
		<key>Resources/gui/panels/create_panel.py</key>
		<data>
		Ei68aHdj0sP21neex6OPD+HAnB4=
		</data>
		<key>Resources/gui/panels/print_panel.py</key>
		<data>
		xA3Cw1THaW6d+RTcVu5S6Yi5aHE=
		</data>
		<key>Resources/gui/panels/resource_count_panel.py</key>
		<data>
		Jbk03e+KzRoFmlgDJMoitSkW31A=
		</data>
		<key>Resources/gui/panels/update_panel.py</key>
		<data>
		6SJ6Kzre+C7ZBMHkqZUsvz1umdE=
		</data>
		<key>Resources/icon-windowed.icns</key>
		<data>
		eEHOuYpZLB0vKGVIWGZOh5rH8+o=
		</data>
		<key>Resources/utils/__pycache__/csv_handler.cpython-39.pyc</key>
		<data>
		UsOn2amC66ZecTdFqNNYw2eRv6Y=
		</data>
		<key>Resources/utils/__pycache__/file_path_manager.cpython-311.pyc</key>
		<data>
		rk7l8DcIDDw5V0UiqUxPHre3Ork=
		</data>
		<key>Resources/utils/__pycache__/file_path_manager.cpython-39.pyc</key>
		<data>
		TrCsSbdK+OdUQWfW9/PQVQy/1DY=
		</data>
		<key>Resources/utils/__pycache__/output_redirector.cpython-311.pyc</key>
		<data>
		itR+oynlPeHaRVspe0V/fxmd8+o=
		</data>
		<key>Resources/utils/csv_handler.py</key>
		<data>
		5DsUvmvalXKp8lRu3rIoRooSC2U=
		</data>
		<key>Resources/utils/file_path_manager.py</key>
		<data>
		8yBh6xbbLC9JzgyNJFDopIR1PtI=
		</data>
		<key>Resources/utils/output_redirector.py</key>
		<data>
		sTQ+QwN/d6AWpxxuB4pa6CJuxmo=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Frameworks/Python</key>
		<dict>
			<key>symlink</key>
			<string>Python.framework/Versions/3.9/Python</string>
		</dict>
		<key>Frameworks/Python.framework</key>
		<dict>
			<key>cdhash</key>
			<data>
			FKwix/oncUwdJ2XBgG90qUSjhQ4=
			</data>
			<key>requirement</key>
			<string>cdhash H"14ac22c7fa27714c1d2765c1806f74a944a3850e"</string>
		</dict>
		<key>Frameworks/base_library.zip</key>
		<dict>
			<key>symlink</key>
			<string>../Resources/base_library.zip</string>
		</dict>
		<key>Frameworks/certifi</key>
		<dict>
			<key>symlink</key>
			<string>../Resources/certifi</string>
		</dict>
		<key>Frameworks/config.py</key>
		<dict>
			<key>symlink</key>
			<string>../Resources/config.py</string>
		</dict>
		<key>Frameworks/core</key>
		<dict>
			<key>symlink</key>
			<string>../Resources/core</string>
		</dict>
		<key>Frameworks/gui</key>
		<dict>
			<key>symlink</key>
			<string>../Resources/gui</string>
		</dict>
		<key>Frameworks/kerberos.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			fXLUKOmqw7yOkeBUgiJUGph4eQg=
			</data>
			<key>requirement</key>
			<string>cdhash H"7d72d428e9aac3bc8e91e0548222541a98787908"</string>
		</dict>
		<key>Frameworks/lib-dynload/_bisect.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			CG+A721xWKKA3wil3RNxLfVWzWc=
			</data>
			<key>requirement</key>
			<string>cdhash H"086f80ef6d7158a280df08a5dd13712df556cd67"</string>
		</dict>
		<key>Frameworks/lib-dynload/_blake2.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			Imq0swvGFXoKKha/bBjzhvCu6So=
			</data>
			<key>requirement</key>
			<string>cdhash H"226ab4b30bc6157a0a2a16bf6c18f386f0aee92a"</string>
		</dict>
		<key>Frameworks/lib-dynload/_bz2.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			UTRIcEvuYv6by5nVkkRfc/zZy50=
			</data>
			<key>requirement</key>
			<string>cdhash H"513448704bee62fe9bcb99d592445f73fcd9cb9d"</string>
		</dict>
		<key>Frameworks/lib-dynload/_codecs_cn.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			IxOrpMbC0f7ta8YuyIzC6C6f1/o=
			</data>
			<key>requirement</key>
			<string>cdhash H"2313aba4c6c2d1feed6bc62ec88cc2e82e9fd7fa"</string>
		</dict>
		<key>Frameworks/lib-dynload/_codecs_hk.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			zBqZHuXwqzp/nNV8rLhhlapTiFw=
			</data>
			<key>requirement</key>
			<string>cdhash H"cc1a991ee5f0ab3a7f9cd57cacb86195aa53885c"</string>
		</dict>
		<key>Frameworks/lib-dynload/_codecs_iso2022.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			WGcMuBCqVmANo2Y5cs7VUJ/YWpg=
			</data>
			<key>requirement</key>
			<string>cdhash H"58670cb810aa56600da3663972ced5509fd85a98"</string>
		</dict>
		<key>Frameworks/lib-dynload/_codecs_jp.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			dLsi3p01k2J4SnhjnnBhMLP0XT8=
			</data>
			<key>requirement</key>
			<string>cdhash H"74bb22de9d359362784a78639e706130b3f45d3f"</string>
		</dict>
		<key>Frameworks/lib-dynload/_codecs_kr.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			cOJkE+E+fuS9MWUqxxY/QE3r0bs=
			</data>
			<key>requirement</key>
			<string>cdhash H"70e26413e13e7ee4bd31652ac7163f404debd1bb"</string>
		</dict>
		<key>Frameworks/lib-dynload/_codecs_tw.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			thikg55uSax3YNsaJPDoiKgSs2w=
			</data>
			<key>requirement</key>
			<string>cdhash H"b618a4839e6e49ac7760db1a24f0e888a812b36c"</string>
		</dict>
		<key>Frameworks/lib-dynload/_contextvars.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			+KqWYOHwAN99T6dP6AR47IFYZlk=
			</data>
			<key>requirement</key>
			<string>cdhash H"f8aa9660e1f000df7d4fa74fe80478ec81586659"</string>
		</dict>
		<key>Frameworks/lib-dynload/_csv.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			oUyr4xhJyX3egWgtdrFhGL/e3hc=
			</data>
			<key>requirement</key>
			<string>cdhash H"a14cabe31849c97dde81682d76b16118bfdede17"</string>
		</dict>
		<key>Frameworks/lib-dynload/_datetime.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			NWQMlt+Fr1FcfHb4TdniRgK7t3c=
			</data>
			<key>requirement</key>
			<string>cdhash H"35640c96df85af515c7c76f84dd9e24602bbb777"</string>
		</dict>
		<key>Frameworks/lib-dynload/_decimal.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			UwGGoBn/3MUD7HD/rB7r85FNdMM=
			</data>
			<key>requirement</key>
			<string>cdhash H"530186a019ffdcc503ec70ffac1eebf3914d74c3"</string>
		</dict>
		<key>Frameworks/lib-dynload/_hashlib.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			aMnLwcKxHcMQ6CoLj9LXaHFE/bM=
			</data>
			<key>requirement</key>
			<string>cdhash H"68c9cbc1c2b11dc310e82a0b8fd2d7687144fdb3"</string>
		</dict>
		<key>Frameworks/lib-dynload/_heapq.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			oxfqctfgkTqnLG6IF+UY9znTIek=
			</data>
			<key>requirement</key>
			<string>cdhash H"a317ea72d7e0913aa72c6e8817e518f739d321e9"</string>
		</dict>
		<key>Frameworks/lib-dynload/_json.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			z8lQ5vQ0e/wCMl0kZf82jjzWH78=
			</data>
			<key>requirement</key>
			<string>cdhash H"cfc950e6f4347bfc02325d2465ff368e3cd61fbf"</string>
		</dict>
		<key>Frameworks/lib-dynload/_lzma.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			6TRxCsBFyfNT8TUO0eH8qWE6rPE=
			</data>
			<key>requirement</key>
			<string>cdhash H"e934710ac045c9f353f1350ed1e1fca9613aacf1"</string>
		</dict>
		<key>Frameworks/lib-dynload/_md5.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			Bo5xD0b1FbESKMnizJbvsE3YWHM=
			</data>
			<key>requirement</key>
			<string>cdhash H"068e710f46f515b11228c9e2cc96efb04dd85873"</string>
		</dict>
		<key>Frameworks/lib-dynload/_multibytecodec.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			FJJUMXAyUKAphKOpFQqdx3aIsCM=
			</data>
			<key>requirement</key>
			<string>cdhash H"14925431703250a02984a3a9150a9dc77688b023"</string>
		</dict>
		<key>Frameworks/lib-dynload/_opcode.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			KHfD0e+iOp7bvZ4AOj/rby9ByWs=
			</data>
			<key>requirement</key>
			<string>cdhash H"2877c3d1efa23a9edbbd9e003a3feb6f2f41c96b"</string>
		</dict>
		<key>Frameworks/lib-dynload/_pickle.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			DWyTHohi55jZ1zrCiqCEIkC+fy4=
			</data>
			<key>requirement</key>
			<string>cdhash H"0d6c931e8862e798d9d73ac28aa0842240be7f2e"</string>
		</dict>
		<key>Frameworks/lib-dynload/_posixsubprocess.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			bWV3dI4NCPp7GmfiuZVZA9bMQX0=
			</data>
			<key>requirement</key>
			<string>cdhash H"6d6577748e0d08fa7b1a67e2b9955903d6cc417d"</string>
		</dict>
		<key>Frameworks/lib-dynload/_queue.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			wvkKuewgjNVNzi+AF5Zh2QR1sQ8=
			</data>
			<key>requirement</key>
			<string>cdhash H"c2f90ab9ec208cd54dce2f80179661d90475b10f"</string>
		</dict>
		<key>Frameworks/lib-dynload/_random.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			bxzhcEWZYExdkh4QDY//hZ+BOu4=
			</data>
			<key>requirement</key>
			<string>cdhash H"6f1ce1704599604c5d921e100d8fff859f813aee"</string>
		</dict>
		<key>Frameworks/lib-dynload/_scproxy.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			932rA6kdLd5jiJayvwcZ8XQC/c0=
			</data>
			<key>requirement</key>
			<string>cdhash H"f77dab03a91d2dde638896b2bf0719f17402fdcd"</string>
		</dict>
		<key>Frameworks/lib-dynload/_sha1.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			1Mcg+XYvVrn3GOxPmxF4AvQVjKc=
			</data>
			<key>requirement</key>
			<string>cdhash H"d4c720f9762f56b9f718ec4f9b117802f4158ca7"</string>
		</dict>
		<key>Frameworks/lib-dynload/_sha256.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			Y7eQmDM+/3igf4SrEWTtyxp6wcA=
			</data>
			<key>requirement</key>
			<string>cdhash H"63b79098333eff78a07f84ab1164edcb1a7ac1c0"</string>
		</dict>
		<key>Frameworks/lib-dynload/_sha3.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			7by9QJGyNmnbx/Bu9Tkh1BPn6jA=
			</data>
			<key>requirement</key>
			<string>cdhash H"edbcbd4091b23669dbc7f06ef53921d413e7ea30"</string>
		</dict>
		<key>Frameworks/lib-dynload/_sha512.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			JQZ99iPWGSgXKl0x7ePlDTl3L/A=
			</data>
			<key>requirement</key>
			<string>cdhash H"25067df623d61928172a5d31ede3e50d39772ff0"</string>
		</dict>
		<key>Frameworks/lib-dynload/_socket.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			tQlvZlBEI+6MQoLeptBUy3+LFLk=
			</data>
			<key>requirement</key>
			<string>cdhash H"b5096f66504423ee8c4282dea6d054cb7f8b14b9"</string>
		</dict>
		<key>Frameworks/lib-dynload/_ssl.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			zMfyZ/qgwZaxLqGfeqG0CJkqoUg=
			</data>
			<key>requirement</key>
			<string>cdhash H"ccc7f267faa0c196b12ea19f7aa1b408992aa148"</string>
		</dict>
		<key>Frameworks/lib-dynload/_statistics.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			zowevex9LBdRE2byzHTzmtJ8PoQ=
			</data>
			<key>requirement</key>
			<string>cdhash H"ce8c1ebdec7d2c17511366f2cc74f39ad27c3e84"</string>
		</dict>
		<key>Frameworks/lib-dynload/_struct.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			SNWA2TsTo7Pz4l35b7/sLvmETic=
			</data>
			<key>requirement</key>
			<string>cdhash H"48d580d93b13a3b3f3e25df96fbfec2ef9844e27"</string>
		</dict>
		<key>Frameworks/lib-dynload/_uuid.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			3RqEDZEbR3peJIUER0GaVvxMlkU=
			</data>
			<key>requirement</key>
			<string>cdhash H"dd1a840d911b477a5e24850447419a56fc4c9645"</string>
		</dict>
		<key>Frameworks/lib-dynload/array.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			w/onthXwt7Xv7+rNNGD8jDaeRLg=
			</data>
			<key>requirement</key>
			<string>cdhash H"c3fa27b615f0b7b5efefeacd3460fc8c369e44b8"</string>
		</dict>
		<key>Frameworks/lib-dynload/binascii.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			5UEZuWek72oIMk0f7dMlzhbOuyY=
			</data>
			<key>requirement</key>
			<string>cdhash H"e54119b967a4ef6a08324d1fedd325ce16cebb26"</string>
		</dict>
		<key>Frameworks/lib-dynload/grp.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			GQfluO6IZxDFUDOuau7SNiZpwkE=
			</data>
			<key>requirement</key>
			<string>cdhash H"1907e5b8ee886710c55033ae6aeed2362669c241"</string>
		</dict>
		<key>Frameworks/lib-dynload/math.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			aE+2E8DqqDTFCL51muG0iOuQS4k=
			</data>
			<key>requirement</key>
			<string>cdhash H"684fb613c0eaa834c508be759ae1b488eb904b89"</string>
		</dict>
		<key>Frameworks/lib-dynload/pyexpat.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			dfr4lhtPOpkmOawqNhpiUnljw7M=
			</data>
			<key>requirement</key>
			<string>cdhash H"75faf8961b4f3a992639ac2a361a62527963c3b3"</string>
		</dict>
		<key>Frameworks/lib-dynload/resource.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			eS/vXIEwUS7KnCkF0S9o5bxFRd4=
			</data>
			<key>requirement</key>
			<string>cdhash H"792fef5c8130512eca9c2905d12f68e5bc4545de"</string>
		</dict>
		<key>Frameworks/lib-dynload/select.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			xF/yKEL7IWOYayA8DeX5L6z12fI=
			</data>
			<key>requirement</key>
			<string>cdhash H"c45ff22842fb2163986b203c0de5f92facf5d9f2"</string>
		</dict>
		<key>Frameworks/lib-dynload/termios.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			GipFicNImyu0l3Ivqd/uLuXUpLw=
			</data>
			<key>requirement</key>
			<string>cdhash H"1a2a4589c3489b2bb497722fa9dfee2ee5d4a4bc"</string>
		</dict>
		<key>Frameworks/lib-dynload/unicodedata.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			qWvwldYFRIgWwTaFwp5IxfAyBeA=
			</data>
			<key>requirement</key>
			<string>cdhash H"a96bf095d605448816c13685c29e48c5f03205e0"</string>
		</dict>
		<key>Frameworks/lib-dynload/zlib.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			+uBrk0qI8/OFI9AXH/ic6HEKM3w=
			</data>
			<key>requirement</key>
			<string>cdhash H"fae06b934a88f3f38523d0171ff89ce8710a337c"</string>
		</dict>
		<key>Frameworks/libwx_baseu-*******.0.dylib</key>
		<dict>
			<key>symlink</key>
			<string>wx/libwx_baseu-*******.0.dylib</string>
		</dict>
		<key>Frameworks/libwx_baseu_net-*******.0.dylib</key>
		<dict>
			<key>symlink</key>
			<string>wx/libwx_baseu_net-*******.0.dylib</string>
		</dict>
		<key>Frameworks/libwx_osx_cocoau_core-*******.0.dylib</key>
		<dict>
			<key>symlink</key>
			<string>wx/libwx_osx_cocoau_core-*******.0.dylib</string>
		</dict>
		<key>Frameworks/libwx_osx_cocoau_html-*******.0.dylib</key>
		<dict>
			<key>symlink</key>
			<string>wx/libwx_osx_cocoau_html-*******.0.dylib</string>
		</dict>
		<key>Frameworks/utils</key>
		<dict>
			<key>symlink</key>
			<string>../Resources/utils</string>
		</dict>
		<key>Frameworks/wx/_adv.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			I5KLqmSceUoEHeVafmFiKuXai+k=
			</data>
			<key>requirement</key>
			<string>cdhash H"23928baa649c794a041de55a7e61622ae5da8be9"</string>
		</dict>
		<key>Frameworks/wx/_core.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			ToYzEZuLnTNTzBIhOykNdEmv4PQ=
			</data>
			<key>requirement</key>
			<string>cdhash H"4e8633119b8b9d3353cc12213b290d7449afe0f4"</string>
		</dict>
		<key>Frameworks/wx/_html.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			eYJG7OPQMiBUT8ILyzClPimBGz4=
			</data>
			<key>requirement</key>
			<string>cdhash H"798246ece3d03220544fc20bcb30a53e29811b3e"</string>
		</dict>
		<key>Frameworks/wx/libwx_baseu-*******.0.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			7abBP7h4F7Suhj9XP7A+rb/LIX4=
			</data>
			<key>requirement</key>
			<string>cdhash H"eda6c13fb87817b4ae863f573fb03eadbfcb217e"</string>
		</dict>
		<key>Frameworks/wx/libwx_baseu_net-*******.0.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			EWO+ubyKRHWG+U1RuxeZqiywqb8=
			</data>
			<key>requirement</key>
			<string>cdhash H"1163beb9bc8a447586f94d51bb1799aa2cb0a9bf"</string>
		</dict>
		<key>Frameworks/wx/libwx_osx_cocoau_core-*******.0.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			L1BPhBsDkWybSy9YnFJJXuBM558=
			</data>
			<key>requirement</key>
			<string>cdhash H"2f504f841b03916c9b4b2f589c52495ee04ce79f"</string>
		</dict>
		<key>Frameworks/wx/libwx_osx_cocoau_html-*******.0.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			H6ws/c7n9xoLK7nXWw5SdDf6DRE=
			</data>
			<key>requirement</key>
			<string>cdhash H"1fac2cfdcee7f71a0b2bb9d75b0e527437fa0d11"</string>
		</dict>
		<key>Frameworks/wx/siplib.cpython-39-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			PgPtSEU6HeObDrQxBLKVAxf99UM=
			</data>
			<key>requirement</key>
			<string>cdhash H"3e03ed48453a1de39b0eb43104b2950317fdf543"</string>
		</dict>
		<key>Resources/Python</key>
		<dict>
			<key>symlink</key>
			<string>Python.framework/Versions/3.9/Python</string>
		</dict>
		<key>Resources/Python.framework</key>
		<dict>
			<key>symlink</key>
			<string>../Frameworks/Python.framework</string>
		</dict>
		<key>Resources/base_library.zip</key>
		<dict>
			<key>hash2</key>
			<data>
			x3AieSomeuKv65/gvjenh1bx6s9FzyHeX3CyjxvcxTg=
			</data>
		</dict>
		<key>Resources/certifi/cacert.pem</key>
		<dict>
			<key>hash2</key>
			<data>
			3l8CcWt/qL42030rGieD3SLufICFX0bYtGhDl/EXVPI=
			</data>
		</dict>
		<key>Resources/certifi/py.typed</key>
		<dict>
			<key>hash2</key>
			<data>
			47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=
			</data>
		</dict>
		<key>Resources/config.py</key>
		<dict>
			<key>hash2</key>
			<data>
			7L/lhfvj0A8LsW/BpwBtBAz0AGfAYWGpv7IsAxvtDnU=
			</data>
		</dict>
		<key>Resources/core/Resource.py</key>
		<dict>
			<key>hash2</key>
			<data>
			2KvtJxa0yVkKrxxP3jvKgRPNEswHh5rNJwULazYQWX8=
			</data>
		</dict>
		<key>Resources/core/__init__.py</key>
		<dict>
			<key>hash2</key>
			<data>
			nSDRPmchUhff/0STYpzGcfhf9hNMxrEdupnoWtTF8y0=
			</data>
		</dict>
		<key>Resources/core/__pycache__/Resource.cpython-311.pyc</key>
		<dict>
			<key>hash2</key>
			<data>
			kB9U+e3/wMfLf5WFnMSZm9g1gLsldKul+o64yFrbVXo=
			</data>
		</dict>
		<key>Resources/core/__pycache__/__init__.cpython-311.pyc</key>
		<dict>
			<key>hash2</key>
			<data>
			VuEW7pXKhulLkw7joKCDVuQ4kH9Kacs3x156LbOTf44=
			</data>
		</dict>
		<key>Resources/core/__pycache__/__init__.cpython-39.pyc</key>
		<dict>
			<key>hash2</key>
			<data>
			jFwy+PJB4VMlqJOO7Usd/q+egqvyKyPoc3ZOa9KktCE=
			</data>
		</dict>
		<key>Resources/core/__pycache__/application_service.cpython-311.pyc</key>
		<dict>
			<key>hash2</key>
			<data>
			TAHeOVLDefLytUpaT5RZqLAbugJf/DEPNtQTLiRneww=
			</data>
		</dict>
		<key>Resources/core/__pycache__/application_service.cpython-39.pyc</key>
		<dict>
			<key>hash2</key>
			<data>
			fVhidgvzXYIi9nKFE0NBKRnGwG3f7GDJ0x+Dx83gyGM=
			</data>
		</dict>
		<key>Resources/core/__pycache__/data_access_layer.cpython-311.pyc</key>
		<dict>
			<key>hash2</key>
			<data>
			CJPFCw2aFt0DGHSZviaMW3q4B8ftruCsU5rlZRjQroE=
			</data>
		</dict>
		<key>Resources/core/__pycache__/exceptions.cpython-311.pyc</key>
		<dict>
			<key>hash2</key>
			<data>
			a5oVxP/CLMIuNI8IXDwdOnpXXm5bR5Rfwu5EdAdx2M4=
			</data>
		</dict>
		<key>Resources/core/__pycache__/exceptions.cpython-39.pyc</key>
		<dict>
			<key>hash2</key>
			<data>
			320ckc02Sg8tKF+Z6IFhlGwTfngVisJCCUeokaU3bhU=
			</data>
		</dict>
		<key>Resources/core/__pycache__/interfaces.cpython-311.pyc</key>
		<dict>
			<key>hash2</key>
			<data>
			eXEux1IFBs27udGRYS56SBU3SbFnQdoPMah3PJPDNsA=
			</data>
		</dict>
		<key>Resources/core/__pycache__/interfaces.cpython-39.pyc</key>
		<dict>
			<key>hash2</key>
			<data>
			wi/WfGHF0Upk1rNXl3y1V/+RtAy0lMOFwa6ElLktOV4=
			</data>
		</dict>
		<key>Resources/core/__pycache__/res2resdef.cpython-311.pyc</key>
		<dict>
			<key>hash2</key>
			<data>
			vIC0f0vcfAC3sFm7mEMu6nPT6NZhpPE4E6RuOkHjUrY=
			</data>
		</dict>
		<key>Resources/core/__pycache__/resource_client.cpython-311.pyc</key>
		<dict>
			<key>hash2</key>
			<data>
			iJkD2I7scq0BbdTJj8KoMA5Wl1+yxtfCRKqAzCRgpPg=
			</data>
		</dict>
		<key>Resources/core/__pycache__/resource_client.cpython-39.pyc</key>
		<dict>
			<key>hash2</key>
			<data>
			KJJAmmmL3exrIJnGuYOuFxiWAimWonTAdWmKkvvx1iM=
			</data>
		</dict>
		<key>Resources/core/__pycache__/resource_core.cpython-311.pyc</key>
		<dict>
			<key>hash2</key>
			<data>
			isLZtZY255nuEusQxVeCS8dhSJqQnPJOIuw6pkkzInU=
			</data>
		</dict>
		<key>Resources/core/__pycache__/resource_manager.cpython-311.pyc</key>
		<dict>
			<key>hash2</key>
			<data>
			5/mu+Iv6hekvpNg5EVAeqySxjOe6S4fsLmGhAHcWiVg=
			</data>
		</dict>
		<key>Resources/core/__pycache__/resource_manager.cpython-39.pyc</key>
		<dict>
			<key>hash2</key>
			<data>
			Zl1KQf9xL6foiJ+dzmpSAyT/hRjBR5Ss/Kb8fP9+OSQ=
			</data>
		</dict>
		<key>Resources/core/__pycache__/response_models.cpython-311.pyc</key>
		<dict>
			<key>hash2</key>
			<data>
			xtl4BbUzpx2aH35U3SXakdyNmyt8aHpnMZtYj+vUXjE=
			</data>
		</dict>
		<key>Resources/core/__pycache__/response_models.cpython-39.pyc</key>
		<dict>
			<key>hash2</key>
			<data>
			uuvvjCbkQhjc9cTT6t6uJ5QABCaeIuE/aXzZq1xSxMg=
			</data>
		</dict>
		<key>Resources/core/application_service.py</key>
		<dict>
			<key>hash2</key>
			<data>
			JyCHS0vXM46iUH6jcG1Yh6n/EW11ewKm++PeyJekRjI=
			</data>
		</dict>
		<key>Resources/core/data_access_layer.py</key>
		<dict>
			<key>hash2</key>
			<data>
			oYrWg1jvh5/Ve+HXeGZ5cDHU/LjHT/3QJC7MagFEg9U=
			</data>
		</dict>
		<key>Resources/core/data_access_layer.py.backup</key>
		<dict>
			<key>hash2</key>
			<data>
			NT934LqV4YQ9m7Y/Ob40nw4124WgapDKON3bbNIiswY=
			</data>
		</dict>
		<key>Resources/core/data_access_layer_broken.py</key>
		<dict>
			<key>hash2</key>
			<data>
			38LB8b9hl2Y2g2V8PC0O+0XB49rG1ahEDaTIdSxU2SY=
			</data>
		</dict>
		<key>Resources/core/dto.py</key>
		<dict>
			<key>hash2</key>
			<data>
			FwwrlqOOFu0WZkCu3Fx82CXOWau655NHvGFFwUAdB2E=
			</data>
		</dict>
		<key>Resources/core/exceptions.py</key>
		<dict>
			<key>hash2</key>
			<data>
			sYJwN2OHP9GRotieGpaagHadrdUBYNhgfOpqWAayf+M=
			</data>
		</dict>
		<key>Resources/core/interfaces.py</key>
		<dict>
			<key>hash2</key>
			<data>
			Jr0jIzmViKRwW1SUrkrKfchxFBXG4PX23NNY9lh9rzc=
			</data>
		</dict>
		<key>Resources/core/res2resdef.py</key>
		<dict>
			<key>hash2</key>
			<data>
			qN9Cqxbhoij7VfHJ39/xNdAXMnqT1LlwcPJCl/lHlKg=
			</data>
		</dict>
		<key>Resources/core/resource_client.py</key>
		<dict>
			<key>hash2</key>
			<data>
			7hf77cdMkBeG+Qpekc1AcHRWmSFThvKW11FlOGpD02w=
			</data>
		</dict>
		<key>Resources/core/resource_core.py</key>
		<dict>
			<key>hash2</key>
			<data>
			jssI+ivDdxuXw9BSc/upuSzqqMfNp1HDAYY8q7MgIgA=
			</data>
		</dict>
		<key>Resources/core/resource_create.csv</key>
		<dict>
			<key>hash2</key>
			<data>
			0if2r8z7Fh0Md9XgTlMfIhYAAs4/0xTW7NbsM2sx24Q=
			</data>
		</dict>
		<key>Resources/core/resource_dup.csv</key>
		<dict>
			<key>hash2</key>
			<data>
			Qd7sp7+8hHo8h4dwfWb7Csrnmu56De+oljtNXln82+k=
			</data>
		</dict>
		<key>Resources/core/resource_manager.py</key>
		<dict>
			<key>hash2</key>
			<data>
			RE2XVmIM7BBRlgsQ1B4KfZ9ktbZ/O7EMnORV1DXpalI=
			</data>
		</dict>
		<key>Resources/core/resource_manager.py.backup</key>
		<dict>
			<key>hash2</key>
			<data>
			R6q1PR4JKIQPsW4bOwVy+S7CT9llr9u5WPr8vzNYxvw=
			</data>
		</dict>
		<key>Resources/core/resource_update_template.csv</key>
		<dict>
			<key>hash2</key>
			<data>
			AIT2c1v67rLawVP9NBdJi8HDRNMU1Uz8b7RLwzwtFQ0=
			</data>
		</dict>
		<key>Resources/core/response_models.py</key>
		<dict>
			<key>hash2</key>
			<data>
			huhHNwY/06+6Na6Ywpirc0XEdQrqJk9V+6Ki/h7UQik=
			</data>
		</dict>
		<key>Resources/gui/__init__.py</key>
		<dict>
			<key>hash2</key>
			<data>
			GRlZijzfc/Vegs04dkpO2r/gzGkxC8uvn+gvgzE5fpk=
			</data>
		</dict>
		<key>Resources/gui/__pycache__/__init__.cpython-311.pyc</key>
		<dict>
			<key>hash2</key>
			<data>
			WQnNL/siIn/JR3NUG8CetuVswKRh2RObsDrjSs1Hq0o=
			</data>
		</dict>
		<key>Resources/gui/__pycache__/main_frame.cpython-311.pyc</key>
		<dict>
			<key>hash2</key>
			<data>
			Ymv08Bk6uku+U7n4Wan6QkujUiycS+/kevkKteD7euk=
			</data>
		</dict>
		<key>Resources/gui/main_frame.py</key>
		<dict>
			<key>hash2</key>
			<data>
			jDtTdczzMMaNPHHiceAaq+4i/75yQyUjCc09d4NokUk=
			</data>
		</dict>
		<key>Resources/gui/panels/__pycache__/create_panel.cpython-311.pyc</key>
		<dict>
			<key>hash2</key>
			<data>
			r68+fM3EsmQisGqUZl2C5T39YPBS1dsn2x6vL4yjPvw=
			</data>
		</dict>
		<key>Resources/gui/panels/__pycache__/print_panel.cpython-311.pyc</key>
		<dict>
			<key>hash2</key>
			<data>
			mo5DomqzDuDzG15AO5XEMBpeRbv+jG4FJ2E4HDW5DSs=
			</data>
		</dict>
		<key>Resources/gui/panels/__pycache__/resource_count_panel.cpython-311.pyc</key>
		<dict>
			<key>hash2</key>
			<data>
			LpyqvYmkTeJdfy6opixZSlxqZqRRxmvIOfEuI/uZEhk=
			</data>
		</dict>
		<key>Resources/gui/panels/__pycache__/update_panel.cpython-311.pyc</key>
		<dict>
			<key>hash2</key>
			<data>
			GGvMIUzNhTStqWPFCudm19BYnXazv2WKm1on8ZWEexg=
			</data>
		</dict>
		<key>Resources/gui/panels/create_panel.py</key>
		<dict>
			<key>hash2</key>
			<data>
			wcXBwW2C0/+Y//PMps54QUNlx3fRHdzm4FnllG1aDns=
			</data>
		</dict>
		<key>Resources/gui/panels/print_panel.py</key>
		<dict>
			<key>hash2</key>
			<data>
			j1zrS0jinVeSQuQBnZAYcT560lLAo8HOnka8yC5Iq58=
			</data>
		</dict>
		<key>Resources/gui/panels/resource_count_panel.py</key>
		<dict>
			<key>hash2</key>
			<data>
			UK3Iv/ZtNhIi26abAyuiN8EK6cpbtYbyZv1BIoEax2o=
			</data>
		</dict>
		<key>Resources/gui/panels/update_panel.py</key>
		<dict>
			<key>hash2</key>
			<data>
			nStvuqidzRwkZFWUG0QBRVauaJ2EKRB8g5KP5TAdEhE=
			</data>
		</dict>
		<key>Resources/icon-windowed.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			uQo7VuWRab4Phv4EEGmfQsyqFqDIXZgO8OtgaAMvCzY=
			</data>
		</dict>
		<key>Resources/kerberos.cpython-39-darwin.so</key>
		<dict>
			<key>symlink</key>
			<string>../Frameworks/kerberos.cpython-39-darwin.so</string>
		</dict>
		<key>Resources/lib-dynload</key>
		<dict>
			<key>symlink</key>
			<string>../Frameworks/lib-dynload</string>
		</dict>
		<key>Resources/libwx_baseu-*******.0.dylib</key>
		<dict>
			<key>symlink</key>
			<string>wx/libwx_baseu-*******.0.dylib</string>
		</dict>
		<key>Resources/libwx_baseu_net-*******.0.dylib</key>
		<dict>
			<key>symlink</key>
			<string>wx/libwx_baseu_net-*******.0.dylib</string>
		</dict>
		<key>Resources/libwx_osx_cocoau_core-*******.0.dylib</key>
		<dict>
			<key>symlink</key>
			<string>wx/libwx_osx_cocoau_core-*******.0.dylib</string>
		</dict>
		<key>Resources/libwx_osx_cocoau_html-*******.0.dylib</key>
		<dict>
			<key>symlink</key>
			<string>wx/libwx_osx_cocoau_html-*******.0.dylib</string>
		</dict>
		<key>Resources/utils/__pycache__/csv_handler.cpython-39.pyc</key>
		<dict>
			<key>hash2</key>
			<data>
			a3B+WO3LGEDAj6t2E3Dt0jgbdMgqDjJkgQy1kTs4mDA=
			</data>
		</dict>
		<key>Resources/utils/__pycache__/file_path_manager.cpython-311.pyc</key>
		<dict>
			<key>hash2</key>
			<data>
			5OG+/b/AuOoDMFgOreYwR59yd0fjbR5I3Hfaz4gTLmY=
			</data>
		</dict>
		<key>Resources/utils/__pycache__/file_path_manager.cpython-39.pyc</key>
		<dict>
			<key>hash2</key>
			<data>
			G9TPA3bGI3hle+Nv6mb2uo+zEfdsBKRfV/nTz7UaQgw=
			</data>
		</dict>
		<key>Resources/utils/__pycache__/output_redirector.cpython-311.pyc</key>
		<dict>
			<key>hash2</key>
			<data>
			LDVao+kiae+hEpUS/4CoAVBVg/QZeXP2zrZRgXo7tos=
			</data>
		</dict>
		<key>Resources/utils/csv_handler.py</key>
		<dict>
			<key>hash2</key>
			<data>
			W8AYXU2I7bk1huq13A9QyJizJC/OsVqfMCQBTiOqAsE=
			</data>
		</dict>
		<key>Resources/utils/file_path_manager.py</key>
		<dict>
			<key>hash2</key>
			<data>
			ako4YABqq2GcnsZuNdNKCNS2BV0k32oiGVkqqVlQQVE=
			</data>
		</dict>
		<key>Resources/utils/output_redirector.py</key>
		<dict>
			<key>hash2</key>
			<data>
			stsvJnEJAQmcfPa8h/k6DdwvUdvUlBjXOo17DiTXZ6E=
			</data>
		</dict>
		<key>Resources/wx</key>
		<dict>
			<key>symlink</key>
			<string>../Frameworks/wx</string>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^Resources/</key>
		<true/>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^(Frameworks|SharedFrameworks|PlugIns|Plug-ins|XPCServices|Helpers|MacOS|Library/(Automator|Spotlight|LoginItems))/</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^[^/]+$</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
