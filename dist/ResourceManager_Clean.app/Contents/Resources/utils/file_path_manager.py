"""
File Path Manager for Resource Manager application.

This module centralizes all file path operations and provides
a unified interface for file handling across the application.
"""

from pathlib import Path
from typing import Optional, Union
from datetime import datetime
from config import config


class FilePathManager:
    """Centralized file path management for the Resource Manager application."""
    
    def __init__(self):
        """Initialize FilePathManager and ensure required directories exist."""
        self.ensure_directories()
    
    def ensure_directories(self) -> None:
        """Ensure all required directories exist."""
        config.ensure_directories()
    
    def get_core_file_path(self, filename: str) -> Path:
        """
        Get full path for files in the core directory.
        
        Args:
            filename: Name of the file
            
        Returns:
            Path object for the file in core directory
        """
        return config.CORE_DIR / filename
    
    def get_csv_file_path(self, filename: str) -> Path:
        """
        Get full path for CSV files in the core directory.
        
        Args:
            filename: Name of the CSV file
            
        Returns:
            Path object for the CSV file
        """
        return config.get_csv_file_path(filename)
    
    def get_output_file_path(self, filename: str) -> Path:
        """
        Get full path for output files in the print_output directory.
        
        Args:
            filename: Name of the output file
            
        Returns:
            Path object for the output file
        """
        return config.get_output_file_path(filename)
    
    def get_timestamped_filename(self, base_name: str, extension: str = "csv") -> str:
        """
        Generate a timestamped filename.
        
        Args:
            base_name: Base name for the file
            extension: File extension (default: csv)
            
        Returns:
            Timestamped filename
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        return f"{base_name}_{timestamp}.{extension}"
    
    def get_timestamped_output_path(self, base_name: str, extension: str = "csv") -> Path:
        """
        Get full path for a timestamped output file.
        
        Args:
            base_name: Base name for the file
            extension: File extension (default: csv)
            
        Returns:
            Path object for the timestamped output file
        """
        filename = self.get_timestamped_filename(base_name, extension)
        return self.get_output_file_path(filename)
    
    def file_exists(self, file_path: Union[str, Path]) -> bool:
        """
        Check if a file exists.
        
        Args:
            file_path: Path to the file
            
        Returns:
            True if file exists, False otherwise
        """
        return Path(file_path).exists()
    
    def remove_file_if_exists(self, file_path: Union[str, Path]) -> bool:
        """
        Remove a file if it exists.
        
        Args:
            file_path: Path to the file
            
        Returns:
            True if file was removed, False if file didn't exist
        """
        path = Path(file_path)
        if path.exists():
            path.unlink()
            return True
        return False
    
    def validate_file_path(self, file_path: Union[str, Path]) -> Path:
        """
        Validate and convert file path to Path object.
        
        Args:
            file_path: Path to validate
            
        Returns:
            Validated Path object
            
        Raises:
            FileNotFoundError: If file doesn't exist
            ValueError: If path is invalid
        """
        path = Path(file_path)
        if not path.exists():
            raise FileNotFoundError(f"File not found: {path}")
        if not path.is_file():
            raise ValueError(f"Path is not a file: {path}")
        return path
    
    def get_resource_create_csv_path(self) -> Path:
        """Get path for resource creation CSV file."""
        return self.get_csv_file_path(config.RESOURCE_CREATE_CSV)
    
    def get_resource_update_csv_path(self) -> Path:
        """Get path for resource update CSV file."""
        return self.get_csv_file_path(config.RESOURCE_UPDATE_CSV)
    
    def get_resource_dup_csv_path(self) -> Path:
        """Get path for resource duplicate CSV file."""
        return self.get_csv_file_path(config.RESOURCE_DUP_CSV)
    
    def get_resource_update_template_csv_path(self) -> Path:
        """Get path for resource update template CSV file."""
        return self.get_csv_file_path(config.RESOURCE_UPDATE_TEMPLATE_CSV)
    
    def get_label_print_output_path(self) -> Path:
        """Get timestamped path for label print output in ~/Downloads."""
        filename = self.get_timestamped_filename("Label_Print", "csv")
        downloads_dir = Path.home() / "Downloads"
        downloads_dir.mkdir(exist_ok=True)  # Ensure Downloads directory exists
        return downloads_dir / filename
    
    def get_updated_resources_output_path(self) -> Path:
        """Get timestamped path for updated resources output."""
        return self.get_timestamped_output_path("Updated_Resources")
    
    def create_backup_path(self, original_path: Union[str, Path]) -> Path:
        """
        Create a backup path for a given file.
        
        Args:
            original_path: Path to the original file
            
        Returns:
            Path for the backup file
        """
        path = Path(original_path)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_name = f"{path.stem}_backup_{timestamp}{path.suffix}"
        return path.parent / backup_name
    
    def get_relative_path(self, file_path: Union[str, Path], base_path: Optional[Union[str, Path]] = None) -> str:
        """
        Get relative path from base path.
        
        Args:
            file_path: Path to make relative
            base_path: Base path (default: project root)
            
        Returns:
            Relative path as string
        """
        path = Path(file_path)
        base = Path(base_path) if base_path else config.PROJECT_ROOT
        try:
            return str(path.relative_to(base))
        except ValueError:
            # If paths are not relative, return absolute path
            return str(path.absolute())


# Global instance
file_path_manager = FilePathManager()
