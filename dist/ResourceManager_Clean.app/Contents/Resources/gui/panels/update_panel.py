import wx
import wx.adv
import threading
import os

class UpdatePanel(wx.Panel):
    def __init__(self, parent, output_redirector, app_service):
        super().__init__(parent)
        self.output_redirector = output_redirector
        self.app_service = app_service
        
        sizer = wx.BoxSizer(wx.VERTICAL)
        
        # Add header and description
        sizer.Add(wx.StaticText(self, label="Update Resources"), flag=wx.ALL, border=5)
        
        # Add CSV format requirements
        req_text = (
            "CSV file must include the following columns:\n"
            "* resourceID* (required): The ID of the resource to update\n"
            "* Title (optional): New title for the resource\n"
            "* Pri (optional): Priority value (0-4)\n"
            "* resdef* (optional): Resource definition ID\n"
            "* location (optional): Specific location"
        )
        req_label = wx.StaticText(self, label=req_text)
        req_label.Wrap(400)  # Wrap text at 400 pixels
        sizer.Add(req_label, 0, wx.ALL | wx.EXPAND, 10)
        
        # Add template download button
        template_btn = wx.Button(self, label="Download CSV Template")
        template_btn.Bind(wx.EVT_BUTTON, self.download_template)
        template_btn.SetToolTip(wx.ToolTip("Download a template CSV file with the correct format"))
        sizer.Add(template_btn, 0, wx.ALL | wx.ALIGN_CENTER_HORIZONTAL, 5)
        
        # General Update Button
        general_update_btn = wx.Button(self, label="Select CSV File for Update")
        general_update_btn.Bind(wx.EVT_BUTTON, self.resource_update)
        general_update_btn.SetToolTip(wx.ToolTip(
            "Select a CSV file containing resource updates.\n"
            "Each row should contain at least the resourceID* column."
        ))
        sizer.Add(general_update_btn, 0, wx.ALL | wx.ALIGN_CENTER_HORIZONTAL, 10)
        
        # Add a container for the hyperlink control
        self.hyperlink_sizer = wx.BoxSizer(wx.VERTICAL)
        sizer.Add(self.hyperlink_sizer, 0, wx.ALL | wx.ALIGN_CENTER_HORIZONTAL, 10)
        
        # Add example CSV format
        example_text = (
            "Example CSV format:\n"
            "Title*,Label Title,Qty,speed,resdef*,Pri,resourceID*,res_link,resdef_link,location\n"
            "Title[chipset],Title,3,USB3_5,67890,2,213113,rdar://res/213113,rdar://resdef/67890,Aruba"
        )
        example_label = wx.StaticText(self, label=example_text)
        example_label.SetForegroundColour(wx.Colour(128, 128, 128))  # Gray color
        sizer.Add(example_label, 0, wx.ALL | wx.EXPAND, 10)
        
        self.SetSizer(sizer)

    def resource_update(self, event):
        with wx.FileDialog(self, "Open CSV file", wildcard="CSV files (*.csv)|*.csv",
                           style=wx.FD_OPEN | wx.FD_FILE_MUST_EXIST) as fileDialog:
            if fileDialog.ShowModal() == wx.ID_CANCEL:
                return
            file_path = fileDialog.GetPath()
            self.start_update_thread(file_path)

    def start_update_thread(self, file_path):
        thread = threading.Thread(target=self.threaded_update, args=(file_path,))
        thread.daemon = True
        thread.start()

    def threaded_update(self, file_path):
        try:
            wx.CallAfter(self.output_redirector.write, "Starting resource update...\n")
            response = self.app_service.update_resources(file_path)
            if response.is_success():
                resources = response.data
                if resources:
                    # The first resource contains the combined link
                    wx.CallAfter(self.create_hyperlink, resources[0]['res_link'])

                    # Print summary
                    total = len(resources) - 1  # Subtract 1 for the combined link entry
                    wx.CallAfter(self.output_redirector.write, f"\nUpdate completed. {total} resources updated.\n")
            else:
                wx.CallAfter(self.output_redirector.write, f"Error during resource update: {response.message}\n")
        except Exception as e:
            wx.CallAfter(self.output_redirector.write, f"Error during resource update: {str(e)}\n")

    def create_hyperlink(self, res_id_link):
        # Clear existing hyperlink controls
        self.hyperlink_sizer.Clear(True)
        
        # Create the hyperlink control and add it to the sizer
        hyperlink_ctrl = wx.adv.HyperlinkCtrl(self, label=res_id_link, url=res_id_link)
        self.hyperlink_sizer.Add(hyperlink_ctrl, 0, wx.ALIGN_CENTER_HORIZONTAL | wx.ALL, 5)
        
        # Refresh the layout
        self.Layout()

    def download_template(self, event):
        """Handle template download button click"""
        # Fix path to look in project root directory's core folder
        template_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'core', 'resource_update_template.csv')
        
        with wx.FileDialog(self, "Save CSV template", wildcard="CSV files (*.csv)|*.csv",
                          style=wx.FD_SAVE | wx.FD_OVERWRITE_PROMPT,
                          defaultFile="resource_update_template.csv") as fileDialog:
            
            if fileDialog.ShowModal() == wx.ID_CANCEL:
                return
                
            try:
                dest_path = fileDialog.GetPath()
                import shutil
                shutil.copy2(template_path, dest_path)
                self.output_redirector.write("Template downloaded successfully.\n")
            except Exception as e:
                self.output_redirector.write(f"Error downloading template: {str(e)}\n")
