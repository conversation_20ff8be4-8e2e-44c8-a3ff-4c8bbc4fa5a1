"""
Interface definitions for the Resource Manager application.

This module defines abstract base classes and interfaces to promote
loose coupling and enable dependency injection throughout the application.
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any, Union
from pathlib import Path


class ResourceClientInterface(ABC):
    """Abstract interface for resource client operations."""
    
    @abstractmethod
    def find_resource_definitions(self, criteria: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Find resource definitions based on criteria."""
        pass
    
    @abstractmethod
    def resource_definition_for_id(self, resdef_id: int) -> Dict[str, Any]:
        """Get resource definition by ID."""
        pass
    
    @abstractmethod
    def resource_for_id(self, resource_id: int) -> Dict[str, Any]:
        """Get resource by ID."""
        pass
    
    @abstractmethod
    def fields_for_resource_id(self, resource_id: int, request_fields: List[str]) -> Dict[str, Any]:
        """Get specific fields for a resource."""
        pass
    
    @abstractmethod
    def fields_for_resource_definition_id(self, resdef_id: int, request_fields: List[str]) -> Dict[str, Any]:
        """Get specific fields for a resource definition."""
        pass
    
    @abstractmethod
    def create_resource(self, resource: Dict[str, Any]) -> Any:
        """Create a new resource."""
        pass
    
    @abstractmethod
    def update_resource(self, resource_id: int, resource: Dict[str, Any]) -> Any:
        """Update an existing resource."""
        pass
    
    @abstractmethod
    def refresh_token(self) -> None:
        """Refresh authentication token."""
        pass


class FileHandlerInterface(ABC):
    """Abstract interface for file handling operations."""
    
    @abstractmethod
    def read_csv(self, filepath: Union[str, Path]) -> List[List[str]]:
        """Read CSV file and return data as list of lists."""
        pass
    
    @abstractmethod
    def write_csv(self, header: List[str], data: List[List[str]], filepath: Union[str, Path]) -> None:
        """Write data to CSV file."""
        pass
    
    @abstractmethod
    def file_exists(self, filepath: Union[str, Path]) -> bool:
        """Check if file exists."""
        pass
    
    @abstractmethod
    def remove_file_if_exists(self, filepath: Union[str, Path]) -> bool:
        """Remove file if it exists."""
        pass
    
    @abstractmethod
    def get_timestamped_filename(self, base_name: str, extension: str = "csv") -> str:
        """Generate timestamped filename."""
        pass


class DataProcessorInterface(ABC):
    """Abstract interface for data processing operations."""
    
    @abstractmethod
    def clean_title(self, title: str) -> tuple[str, str]:
        """Clean and process title, return cleaned title and speed_resdef."""
        pass
    
    @abstractmethod
    def extract_speed(self, speed_resdef: str) -> Optional[str]:
        """Extract speed information from speed_resdef string."""
        pass
    
    @abstractmethod
    def extract_resdef(self, speed_resdef: str) -> Optional[str]:
        """Extract resource definition ID from speed_resdef string."""
        pass
    
    @abstractmethod
    def process_resource(self, row: List[str]) -> tuple[str, str, str, str, str]:
        """Process resource row and extract required information."""
        pass


class ResourceServiceInterface(ABC):
    """Abstract interface for resource service operations."""
    
    @abstractmethod
    def search_resource_definition(self, title: str) -> List[Dict[str, Any]]:
        """Search for resource definitions by title."""
        pass
    
    @abstractmethod
    def search_resource_definition_by_id(self, resdef_id: int) -> Dict[str, Any]:
        """Search for resource definition by ID."""
        pass
    
    @abstractmethod
    def search_resource_by_id(self, resource_id: int) -> Dict[str, Any]:
        """Search for resource by ID."""
        pass
    
    @abstractmethod
    def create_resource(self, resdef_id: str, title: str, priority: str, 
                       specific_location: str = '', component_id: Optional[int] = None,
                       dri_id: Optional[int] = None, inventory_keeper_id: Optional[int] = None,
                       category_id: Optional[int] = None) -> int:
        """Create a new resource."""
        pass
    
    @abstractmethod
    def update_resource(self, resource_id: str, title: str, priority: str,
                       resdef_id: str, specific_location: str) -> tuple[int, str, int, str]:
        """Update an existing resource."""
        pass


class ResourceManagerInterface(ABC):
    """Abstract interface for resource manager operations."""
    
    @abstractmethod
    def create_resource(self, title: Optional[str] = None, resdef_id: Optional[str] = None,
                       priority: Optional[str] = None, quantity: Optional[str] = None,
                       location: Optional[str] = None, category_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """Create a single resource."""
        pass
    
    @abstractmethod
    def create_resources_from_csv(self, csv_path: str) -> List[Dict[str, Any]]:
        """Create multiple resources from CSV file."""
        pass
    
    @abstractmethod
    def update_resources(self, csv_path: str) -> List[Dict[str, Any]]:
        """Update resources from CSV file."""
        pass
    
    @abstractmethod
    def print_resources(self, file_path: Optional[str] = None) -> str:
        """Print resources to CSV file."""
        pass


class ConfigInterface(ABC):
    """Abstract interface for configuration management."""
    
    @abstractmethod
    def get_env(self) -> str:
        """Get current environment."""
        pass
    
    @abstractmethod
    def get_api_base_url(self) -> str:
        """Get API base URL."""
        pass
    
    @abstractmethod
    def get_api_timeout(self) -> int:
        """Get API timeout."""
        pass
    
    @abstractmethod
    def ensure_directories(self) -> None:
        """Ensure required directories exist."""
        pass


class LoggerInterface(ABC):
    """Abstract interface for logging operations."""
    
    @abstractmethod
    def debug(self, message: str) -> None:
        """Log debug message."""
        pass
    
    @abstractmethod
    def info(self, message: str) -> None:
        """Log info message."""
        pass
    
    @abstractmethod
    def warning(self, message: str) -> None:
        """Log warning message."""
        pass
    
    @abstractmethod
    def error(self, message: str, exc_info: bool = False) -> None:
        """Log error message."""
        pass


class ValidationInterface(ABC):
    """Abstract interface for data validation operations."""
    
    @abstractmethod
    def validate_resource_data(self, data: Dict[str, Any]) -> bool:
        """Validate resource data."""
        pass
    
    @abstractmethod
    def validate_csv_format(self, filepath: Union[str, Path]) -> bool:
        """Validate CSV file format."""
        pass
    
    @abstractmethod
    def validate_priority(self, priority: Union[str, int]) -> bool:
        """Validate priority value."""
        pass


# Type aliases for better code readability
ResourceData = Dict[str, Any]
ResourceList = List[ResourceData]
CSVData = List[List[str]]
FilePath = Union[str, Path]
