import csv

import core.Resource as Resource
from core.resource_client import TSTTResourceClient
from utils.file_path_manager import file_path_manager
from utils.csv_handler import CSVHandler
from core.exceptions import (
    ResourceManagerException, ValidationError, CategoryIdError
)
from core.interfaces import ResourceManagerInterface

class ResourceManager(ResourceManagerInterface):
    def __init__(self, resource_client: TSTTResourceClient = None):
        """
        Initialize ResourceManager with dependency injection.

        Args:
            resource_client: TSTTResourceClient instance. If None, creates a new instance.
        """
        self.resource_client = resource_client or TSTTResourceClient()
        self.resource = Resource.Resource(self.resource_client)  # Initialize Resource class with client

    def create_resource(self, title=None, resdef_id=None, priority=None, quantity=None, location=None, category_id=None):
        try:
            file_path = file_path_manager.get_resource_create_csv_path()

            file_path_manager.remove_file_if_exists(file_path)

            if not title and not resdef_id:
                raise ValidationError("Please provide either Title or Resource Definition ID.")

            if not quantity:
                raise ValidationError("Please provide the number of resources you want to create.")

            with open(file_path, mode='a', newline='') as file:
                fieldnames = ['Title*', 'Label Title', 'Qty', 'speed', 'resdef*', 'Pri', 'resourceID*', 'res_link', 'resdef_link', 'location', 'category_id']
                writer = csv.DictWriter(file, fieldnames=fieldnames)
                if file_path.stat().st_size == 0:
                    writer.writeheader()
                writer.writerow({
                    'Title*': title,
                    'Pri': priority,
                    'Qty': quantity,
                    'resdef*': resdef_id,
                    'location': location,
                    'category_id': category_id
                })

            return self._create_resources(str(file_path))

        except ValidationError:
            raise
        except Exception as e:
            raise ResourceManagerException(f"Error creating resource: {str(e)}")

    def create_resources_from_csv(self, csv_path):
        """Create multiple resources from a CSV file"""
        return self._create_resources(csv_path)

    def _create_resources(self, file_path):
        """Internal method to handle resource creation"""
        try:
            # print("Resource creation started...")
            res_id_list = self.resource.create_(file_path)

            # Convert the result to the format expected by the GUI
            resources = []
            for row in res_id_list:
                if row[6]:  # If resource ID exists
                    resource = {
                        'resourceID*': str(row[6]),
                        'res_link': f"rdar://res/{row[6]}",
                        'Title*': row[0],
                        'found': False
                    }
                    resources.append(resource)
                    # print(f"Resource created: {row[6]} for {row[0]}")

            # Create a combined resource link for all updated resources
            resource_ids = [str(r['resourceID*']) for r in resources]
            combined_link = f"rdar://res/{','.join(resource_ids)}"

            return [{
                'resourceID*': ','.join(resource_ids),
                'res_link': combined_link,
                'Title*': f'Created {len(resources)} resources',
                'found': False
            }] + resources

        except CategoryIdError:
            raise
        except Exception as e:
            raise ResourceManagerException(f"Error creating resources: {str(e)}")

    def update_resources(self, csv_path):
        """Update resources from a CSV file"""
        try:
            # Ensure output directory exists
            file_path_manager.ensure_directories()
            
            # Read the input file
            data = CSVHandler.read_csv(csv_path)
            
            # Call the actual update_() method from Resource class with the selected file
            print("Resource update started...")
            updated_data = self.resource.update_(csv_path)  # Pass the selected file path directly
            
            # Process the updated data
            resources = []
            for row in updated_data:
                if row[6]:  # If resource ID exists
                    resource = {
                        'resourceID*': str(row[6]),
                        'res_link': f"rdar://res/{row[6]}",
                        'Title*': row[0],
                        'Pri': row[5],
                        'resdef*': row[4],
                        'location': row[9] if len(row) > 9 else '',
                        'found': False
                    }
                    resources.append(resource)
            
            # Create a combined resource link for all updated resources
            resource_ids = [str(r['resourceID*']) for r in resources]
            combined_link = f"rdar://res/{','.join(resource_ids)}"
            
            # Save updated data to output file
            output_file = file_path_manager.get_updated_resources_output_path()
            CSVHandler.write_csv(resources, str(output_file))
            
            # Return list with combined link as first item
            return [{
                'resourceID*': ','.join(resource_ids),
                'res_link': combined_link,
                'Title*': f'Updated {len(resources)} resources',
                'found': False
            }] + resources
            
        except Exception as e:
            raise Exception(f"Error updating resources: {str(e)}")

    def print_resources(self, file_path=None):
        """Print resources to a CSV file"""
        try:
            if file_path is None:
                file_path = file_path_manager.get_resource_dup_csv_path()
            else:
                file_path = file_path_manager.validate_file_path(file_path)

            # Ensure output directory exists
            file_path_manager.ensure_directories()

            # Read the CSV file
            data = CSVHandler.read_csv(file_path)

            if not data:
                raise Exception(f"No data found in {file_path}")

            # Check if data is list of lists or list of dicts
            if isinstance(data[0], list):
                # Data is list of lists (new format)
                header = data[0]  # First row is header
                rows = data[1:]   # Rest are data rows

                # Process each row
                for row in rows:
                    # Find column indices
                    try:
                        resdef_idx = header.index('resdef*') if 'resdef*' in header else -1
                        resource_id_idx = header.index('resourceID*') if 'resourceID*' in header else -1
                        pri_idx = header.index('Pri') if 'Pri' in header else -1

                        # Add resdef_link if resdef* exists and is not empty
                        if resdef_idx >= 0 and len(row) > resdef_idx:
                            resdef_value = str(row[resdef_idx]).strip()
                            if resdef_value and resdef_value.lower() != 'nan':
                                # Add resdef_link column if not exists
                                if 'resdef_link' not in header:
                                    header.append('resdef_link')
                                # Ensure row has enough elements
                                while len(row) < len(header):
                                    row.append('')
                                row[header.index('resdef_link')] = f"rdar://resdef/{resdef_value}"

                        # Add res_link
                        if resource_id_idx >= 0 and len(row) > resource_id_idx:
                            resource_id = str(row[resource_id_idx]).strip()
                            if resource_id:
                                # Add res_link column if not exists
                                if 'res_link' not in header:
                                    header.append('res_link')
                                # Ensure row has enough elements
                                while len(row) < len(header):
                                    row.append('')
                                row[header.index('res_link')] = f"rdar://res/{resource_id}"

                        # Convert Priority
                        if pri_idx >= 0 and len(row) > pri_idx:
                            pri_value = str(row[pri_idx]).strip()
                            if 'Priority' not in header:
                                header.append('Priority')
                            # Ensure row has enough elements
                            while len(row) < len(header):
                                row.append('')

                            # Priority conversion rules:
                            # If Pri value equals 5 (PRIORITY_NA_VALUE), convert to 'N/A'
                            # If Pri value is a number, subtract 1 (e.g., 5→4, 4→3)
                            if pri_value == '5':  # PRIORITY_NA_VALUE = 5
                                row[header.index('Priority')] = 'N/A'
                            elif pri_value.isdigit():
                                row[header.index('Priority')] = str(int(pri_value) - 1)
                            else:
                                row[header.index('Priority')] = pri_value

                    except Exception as e:
                        print(f"Error processing row: {e}")
                        continue

                # If there's only one data row, duplicate it
                if len(rows) == 1:
                    rows.append(rows[0].copy())

                # Combine header and rows for output
                processed_data = [header] + rows

            else:
                # Data is list of dicts (old format) - process as before
                for row in data:
                    # Add resdef_link if resdef* exists and is not empty
                    resdef_value = row.get('resdef*', '').strip()
                    if resdef_value and resdef_value.lower() != 'nan':
                        row["resdef_link"] = f"rdar://resdef/{resdef_value}"

                    # Add res_link
                    resource_id = row.get('resourceID*', '').strip()
                    if resource_id:
                        row["res_link"] = f"rdar://res/{resource_id}"

                    # Convert Priority
                    pri_value = row.get('Pri', '').strip()
                    # Priority conversion rules:
                    # If Pri value equals 5 (PRIORITY_NA_VALUE), convert to 'N/A'
                    # If Pri value is a number, subtract 1 (e.g., 5→4, 4→3)
                    if pri_value == '5':  # PRIORITY_NA_VALUE = 5
                        row["Priority"] = 'N/A'
                    elif pri_value.isdigit():
                        row["Priority"] = str(int(pri_value) - 1)
                    else:
                        row["Priority"] = pri_value

                # If there's only one row, duplicate it
                if len(data) == 1:
                    data.append(data[0].copy())

                processed_data = data

            # Generate output filename with timestamp in Downloads directory
            output_file = file_path_manager.get_label_print_output_path()

            # Save to CSV
            CSVHandler.write_csv(processed_data, str(output_file))
            return str(output_file)

        except Exception as e:
            raise Exception(f"Error printing resources: {str(e)}")
