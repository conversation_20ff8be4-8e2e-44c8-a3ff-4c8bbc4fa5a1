import re
import urllib3
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
from core.resource_client import TSTTResourceClient
import core.res2resdef as res2resdef
from config import config
from core.data_access_layer import create_data_access_layer
from core.interfaces import ResourceServiceInterface, DataProcessorInterface
from core.exceptions import CategoryIdError, ResourceManagerException

class Resource(ResourceServiceInterface, DataProcessorInterface):
    def __init__(self, resource_client: TSTTResourceClient = None):
        """
        Initialize Resource with dependency injection.

        Args:
            resource_client: TSTTResourceClient instance. If None, creates a new instance.
        """
        self.resource_client = resource_client or TSTTResourceClient()
        self.data_access = create_data_access_layer(self.resource_client)

    def search_resource_definition(self, title):
        return self.data_access.find_resource_definitions({'title': f"%{title}%"})

    def search_resource_definition_by_id(self, resdef_id):
        return self.data_access.get_resource_definition_by_id(resdef_id)

    def search_resource_by_id(self, resourceID):
        return self.data_access.get_resource_by_id(int(resourceID))

    def create_resource(self, resdef_id, title, priority, specificLocation='', componentId=None, driId=None, inventoryKeeperId=None, category_id=None):
        # Create a resource in TSTT using the provided data
        try:
            if category_id is not None:
                category = int(category_id)
            else:
                category = self.data_access.get_resource_definition_by_id(int(resdef_id))["category"]["id"]
        except Exception as e:
            raise CategoryIdError(resdef_id)

        if priority != str(config.PRIORITY_NA_VALUE):
            priority_value = int(priority) + 1
        else:
            priority_value = int(priority)

        # Use config defaults if not provided
        componentId = componentId or config.DEFAULT_COMPONENT_ID
        driId = driId or config.DEFAULT_DRI_ID
        inventoryKeeperId = inventoryKeeperId or config.DEFAULT_INVENTORY_KEEPER_ID
        specificLocation = specificLocation or config.DEFAULT_SPECIFIC_LOCATION

        resource = {
            "title": title,
            "componentId": componentId,
            "classId": config.DEFAULT_CLASS_ID,
            "stateId": config.DEFAULT_STATE_ID,
            "categoryId": category,
            "priority": priority_value,
            "locationId": config.DEFAULT_LOCATION_ID,
            "specificLocation": specificLocation,
            "driId": driId,
            "inventoryKeeperId": inventoryKeeperId,
        }
        # specificLocation is already set above from config or parameter
        try:
            response = self.data_access.create_resource(resource)
            resourceID = response.json()['id']
        except Exception as e:
            raise ResourceManagerException(f"Error creating resource: {e}")
        return resourceID

    def update_resource(self, resource_ID, title, priority, resdef_id, specifica_location):
        # update resource according to the csv
        # update title if title row exist, otherwise use resdef title
        # update priority if priority row exist, otherwise use resdef priority
        # Ensure resource_ID is a string before calling replace
        resource_ID_str = str(resource_ID) if resource_ID is not None else ''
        resource_ID_str = resource_ID_str.replace("rdar://res/", '')
        # print(resource_ID_str)
        resource_ID = int(resource_ID_str) if resource_ID_str.strip() else 0
        if title == "":
            if resdef_id:
                try:
                    search_result = self.search_resource_definition_by_id(int(resdef_id))
                    title = search_result['title']
                    title = title + ' (resdef: ' + resdef_id + ')'
                except Exception as e:
                    print(f"Error getting resdef {resdef_id}: {e}")
                    title = f"Resource {resource_ID}"  # Fallback title
            else:
                try:
                    resource_fields = self.data_access.get_resource_fields(int(resource_ID), ['keywords', 'title'])
                    title = resource_fields.get('title', f"Resource {resource_ID}")
                    if title:
                        title = self.clean_prefix(title)
                    else:
                        title = f"Resource {resource_ID}"  # Fallback if title is empty
                except Exception as e:
                    print(f"Error update {resource_ID}: {e}")
                    title = f"Resource {resource_ID}"  # Fallback title
                    return (resource_ID, title, priority, resdef_id)

        # Ensure title is not empty or None
        if not title or title.strip() == "":
            title = f"Resource {resource_ID}"
            print(f"Warning: Empty title for resource {resource_ID}, using fallback: {title}")

        _, speed_resdef = self.clean_title(title)
        resdef_id = self.extract_resdef(speed_resdef)

        if priority == "":
            try:
                priority, _ = self.extract_title_priority(resdef_id)
            except:
                priority = None
        if not priority:
            priority_value = config.PRIORITY_DEFAULT
        else:
            priority_value = int(priority) + 1

        resource = {
            "title": title.strip(),  # Ensure no leading/trailing whitespace
            "componentId": config.DEFAULT_COMPONENT_ID,
            "priority": priority_value,
            "locationId": config.DEFAULT_LOCATION_ID,
            "driId": config.DEFAULT_DRI_ID,
            "assigneeId": config.DEFAULT_ASSIGNEE_ID
        }
        #56361 bamboo /66842 aruba

        if specifica_location:
            resource["specificLocation"] = specifica_location

        try:
            # Skip update if resource_ID is empty or invalid
            if not resource_ID or str(resource_ID).strip() == "":
                print(f"Skipping update for empty resource ID")
                return (resource_ID, title, priority_value, resdef_id)

            self.data_access.update_resource(resource_ID, resource)
            print(f"Successfully updated resource {resource_ID}")
        except Exception as e:
            print(f"Error update resource {resource_ID}: {e}")
        return (resource_ID, title, priority_value, resdef_id)

    def extract_title_priority(self, resdef_id):
        resource_data = self.data_access.get_resource_definition_fields(int(resdef_id), ['keywords', 'title'])
        keywords = [keyword['keyword']['name'] for keyword in resource_data["keywords"]]
        title = resource_data['title']
        # Pattern to match 'ATC USB' followed by digits and 'P' followed by digits
        pattern_usb = r'ATC USB(\d+) P(\d+)'

        # Pattern to match 'ATC TBT' followed by digits and 'P' followed by digits
        pattern_tbt = r'ATC CIO P(\d+)'

        # Iterate over each keyword
        for keyword in keywords:
            # Check if the keyword matches the USB pattern
            match_usb = re.search(pattern_usb, keyword)
            if match_usb:
                # Extract the P value for USB
                pri = match_usb.group(2)
                print(f'found match in {keyword}, {pri}')
                return pri, title

            # Check if the keyword matches the TBT pattern
            match_tbt = re.search(pattern_tbt, keyword)
            if match_tbt:
                # Extract the P value for TBT
                pri = match_tbt.group(1)
                print(f'found match in {keyword}, {pri}')
                return pri, title

        # If no match found, return None
        return None, title

    # CSV processing functions
    def read_csv(self, filepath):
        return self.data_access.read_csv_data(filepath)

    def write_csv(self, header, data, filepath):
        self.data_access.write_csv_data(header, data, filepath)

    def clean_chipset(self, title):
        title = re.sub(r"\[.*\]", '', title)  # 去除[]
        title = re.sub(r"\{.*\}", '', title)  # 去除{}
        return title

    def clean_store(self, title):
        title = re.sub(r"\(Store.*?\)", '', title)  # 去除Store
        return title

    def clean_prefix(self, title):
        title = re.sub(r"\(Shanghai.*?\)", '', title)  # 去除Shanghai
        return title

    # Title processing functions
    def clean_title(self, title):
        # Remove brackets, curly braces, store info, leading/trailing spaces
        title = self.clean_chipset(title)
        title = self.clean_store(title)
        title = self.clean_prefix(title)
        title = title.lstrip()  # 去除空格
        if title.rfind(' - ') == -1:
            new_title = title
        else:
            new_title = title[:title.rfind(' - ')]
        speed_resdef = title[title.rfind(' - ')+3:]
        return (new_title, speed_resdef)

    def extract_speed(self, speed_resdef):
        # Use regular expressions to extract speed and resdef information
        speed = re.search(r'(TBT.*)|(USB3_\w*)|(FS)|(HS)|(USB4_\w*)', speed_resdef)
        if speed:
            if "/" in speed.group():
                speed = re.split(r'/', speed.group())[0]
            else:
                speed = speed.group()
        else:
            speed = None
        return speed

    def extract_resdef(self, speed_resdef):
        try:
            match = re.search(r'\d{5}', speed_resdef)
            if match:
                resdef_id = match.group()
            return resdef_id
        except:
            pass

    def search_speed_resdef_from_title(self, cleaned_title):
        search_result = self.search_resource_definition(cleaned_title)
        priority = None
        speed = None
        resdef_id = None
        title = cleaned_title

        if search_result:
            resdef_id = search_result[0]['resourceDefinitionId']
            title = search_result[0]['title']  # Update cleaned_title for clarity
            speed = self.extract_speed(title)  # Try extracting speed again
            title = title + f' (resdef: {resdef_id})'

            if not priority:
                priority = ''
        else:
            print(f"'{cleaned_title}' does not match server")
            pass
        return (title, resdef_id, speed, priority)
    

    def process_resource(self, row):
        """Extracts speed, resdef_id, priority, and cleaned title from the given row,
        handling missing values gracefully.

        Args:
            row (list): A list containing data for a resource. Expected format:
                [title, speed_resdef, other_data1, other_data2, resdef_id, priority]

        Returns:
            tuple: A tuple containing the extracted values (speed, resdef_id, priority, cleaned_title).

        Raises:
            ValueError: If no title or resource definition information is found.
        """
        title = row[0]
        resdef_id = row[4]
        priority = row[5]
        if resdef_id:
            priority, title = self.extract_title_priority(resdef_id)
            if not priority:
                priority = '5'
            cleaned_title, speed_resdef = self.clean_title(title)  # Re-clean title if necessary
            speed = self.extract_speed(speed_resdef)  # Attempt speed extraction from clean title
            title = title + f' (resdef: {resdef_id})'
        elif title:
            cleaned_title, speed_resdef = self.clean_title(row[0])
            # Extract values from initial fields if possible
            try:
                speed = self.extract_speed(speed_resdef)
                resdef_id = self.extract_resdef(speed_resdef)
            except (KeyError, ValueError):  # Handle cases where speed/resdef is missing in speed_resdef
                print("No resdef info contained in title")
                title, resdef_id, speed, priority = self.search_speed_resdef_from_title(cleaned_title)
            # If necessary, attempt extraction from row values or resource definition
        else:
            raise ValueError("Unable to extract information needed")
        return speed, resdef_id, priority, cleaned_title, title

    def duplicate_rows(self, data):
        """Duplicates rows based on the value in the 4th column (row[3]).

        Args:
            data: A list of lists, where each inner list represents a row of data.

        Returns:
            A list of lists with duplicated rows based on the count value.
        """

        new_data = []
        for row in data:
            count = int(row[2])
            for _ in range(count):
                new_data.append(row.copy())  # Use copy() to avoid shallow copies
        return new_data

    def print_resource(self, file='resource_dup.csv'):
        return self.data_access.process_print_data(file)

    def Add_Resource2Resdef(self):
        # Ensure the file exists before reading
        file_path = self.data_access.ensure_file_exists('resource_dup.csv')

        if file_path.stat().st_size == 0:
            return

        data = self.read_csv('resource_dup.csv')

        if len(data) <= 1:  # Only header or empty
            return

        # Process data rows silently
        for row in data[1:]:
            if len(row) > 6:  # Ensure row has enough columns
                resdef_id = row[4]
                resouceID = row[6]
                if resdef_id and resouceID:  # Ensure values are not empty
                    res2resdef.res2resdef(resouceID, resdef_id)

    def create_(self, file_name='resource_create.csv'):
        # Read data from CSV
        data = self.read_csv(file_name)

        # Skip header row
        header = data[0]
        data = data[1:]

        # Process each resource
        for row in data:
            speed, resdef_id, priority, cleaned_title, title = self.process_resource(row)
            row[0] = title
            row[1] = cleaned_title
            row[4] = str(resdef_id)
            row[5] = priority
            row[3] = speed

        # Dup rows according to qty
        data = self.duplicate_rows(data)
        # Create resources
        for row in data:
            row[6] = self.create_resource(row[4], row[0], row[5], row[9])
            row[5] = int(priority)

        self.data_access.clean_duplicate_file(config.RESOURCE_DUP_CSV)
        # Ensure the file path exists before writing
        self.data_access.ensure_file_exists(config.RESOURCE_DUP_CSV)
        self.write_csv(header, data, config.RESOURCE_DUP_CSV)
        self.Add_Resource2Resdef()
        return data

    def update_(self, file_name='resource_update.csv'):
        data = self.read_csv(file_name)  # Use the provided file name
        header = data[0]
        data = data[1:]
        for row in data:
            priority = row[5]
            resouceID = row[6]
            title = row[0]
            resdef_id = row[4]
            specifica_location = row[9]
            _, title, priority_value, resdef_id = self.update_resource(resouceID, title, priority, resdef_id, specifica_location)
            cleaned_title, speed_resdef = self.clean_title(title)
            speed = self.extract_speed(speed_resdef)
            row[0] = title
            row[1] = cleaned_title
            row[4] = str(resdef_id)
            row[5] = str(priority_value)
            row[3] = speed

        self.data_access.clean_duplicate_file(config.RESOURCE_DUP_CSV)
        # Ensure the file path exists before writing
        self.data_access.ensure_file_exists(config.RESOURCE_DUP_CSV)
        self.write_csv(header, data, config.RESOURCE_DUP_CSV)
        return data

    def print_out(self, file):
        print(f'Loading file: {file}')

def main():
    resource = Resource()
    while True:
        i = input("Update or Create resources, S to skip (U/C/S)")
        if i.lower() == "c":
            resource.create_()
            i = input("是否需要打印resources (y/n)")
            if i == 'y':
                resource.print_resource()
            else:
                exit
            break

        elif i.lower() == 'u':
            resource.update_()
            i = input("是否需要打印resources (y/n)")
            if i == 'y':
                resource.print_resource()
            else:
                exit
            break

        elif i.lower() == 's':
            data = resource.read_csv('resource_update.csv')
            header = data[0]
            data = data[1:]
            for row in data:
                resouceID = row[6]
                search_result = resource.search_resource_by_id(resouceID)
                title = search_result['title']
                cleaned_title, speed_resdef = resource.clean_title(title)
                speed = resource.extract_speed(speed_resdef)
                resdef_id = resource.extract_resdef(speed_resdef)
                print(speed_resdef)
                row[0] = title
                row[1] = cleaned_title
                row[4] = str(resdef_id)
                row[5] = search_result['priority']
                row[3] = speed

            resource.data_access.clean_duplicate_file(config.RESOURCE_DUP_CSV)
            resource.write_csv(header, data, config.RESOURCE_DUP_CSV)
            resource.print_resource()
            break

        else:
            print("Please choose Update or Create")

if __name__ == "__main__":
  main()
