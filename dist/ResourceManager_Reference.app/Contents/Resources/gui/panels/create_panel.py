import wx
import wx.adv
import os
import threading


class CreatePanel(wx.Panel):
    def __init__(self, parent, output_redirector, app_service):
        super().__init__(parent)
        self.output_redirector = output_redirector
        self.app_service = app_service
        self.hyperlink_ctrl = None
        
        sizer = wx.BoxSizer(wx.VERTICAL)
        
        # Create fields for resource creation
        sizer.Add(wx.StaticText(self, label="Create a New Resource (* indicates required field)"), flag=wx.ALL, border=5)
        
        # Title and ResdefID row
        self.title_input = wx.TextCtrl(self, size=(600, -1))
        self.resdef_id_input = wx.TextCtrl(self, size=(100, -1))
        
        # Arrange the inputs with labels
        input_sizer_1 = wx.FlexGridSizer(0, 4, 10, 10)
        input_sizer_1.Add(wx.StaticText(self, label="Title*"), 0, wx.ALIGN_CENTER_VERTICAL)
        input_sizer_1.Add(self.title_input, 0, wx.EXPAND)
        input_sizer_1.Add(wx.StaticText(self, label="ResdefID*"), 0, wx.ALIGN_CENTER_VERTICAL)
        input_sizer_1.Add(self.resdef_id_input, 0, wx.EXPAND)

        # Add note about Title/ResdefID requirement
        note_text = wx.StaticText(self, label="Note: Either Title or ResdefID must be provided")
        note_text.SetForegroundColour(wx.Colour(128, 128, 128))  # Gray color
        sizer.Add(input_sizer_1, 0, wx.ALL | wx.EXPAND, 10)
        sizer.Add(note_text, 0, wx.ALL, 5)

        # Priority, Qty, and Location row
        self.priority_input = wx.TextCtrl(self, size=(100, -1))
        self.qty_input = wx.TextCtrl(self, size=(100, -1))
        self.location_input = wx.TextCtrl(self, size=(100, -1))

        input_sizer_2 = wx.FlexGridSizer(0, 6, 10, 10)
        input_sizer_2.Add(wx.StaticText(self, label="Priority"), 0, wx.ALIGN_CENTER_VERTICAL)
        input_sizer_2.Add(self.priority_input, 0, wx.EXPAND)
        input_sizer_2.Add(wx.StaticText(self, label="Qty*"), 0, wx.ALIGN_CENTER_VERTICAL)
        input_sizer_2.Add(self.qty_input, 0, wx.EXPAND)
        input_sizer_2.Add(wx.StaticText(self, label="Location"), 0, wx.ALIGN_CENTER_VERTICAL)
        input_sizer_2.Add(self.location_input, 0, wx.EXPAND)

        # Add Category ID field
        self.category_input = wx.TextCtrl(self, size=(100, -1))
        input_sizer_2.Add(wx.StaticText(self, label="Category ID"), 0, wx.ALIGN_CENTER_VERTICAL)
        input_sizer_2.Add(self.category_input, 0, wx.EXPAND)
        
        # Update tooltips
        self.title_input.SetToolTip(wx.ToolTip("Required if ResdefID is not provided"))
        self.resdef_id_input.SetToolTip(wx.ToolTip("Required if Title is not provided"))
        self.priority_input.SetToolTip(wx.ToolTip("Optional - defaults to 5 if not provided"))
        self.qty_input.SetToolTip(wx.ToolTip("Required - number of resources to create"))
        self.location_input.SetToolTip(wx.ToolTip("Optional - defaults to 'Aruba' if not provided"))
        self.category_input.SetToolTip(wx.ToolTip("Required if ResdefID is invalid or not provided"))
       
        input_sizer_2.AddGrowableCol(1)
        input_sizer_2.AddGrowableCol(3)
        input_sizer_2.AddGrowableCol(5)

        sizer.Add(input_sizer_2, 0, wx.ALL | wx.EXPAND, 10)

        # Horizontal sizer for Create button and hyperlink
        button_hyperlink_sizer = wx.FlexGridSizer(0, 0, 10, 10)
        create_button = wx.Button(self, label="Create Resource")
        create_button.Bind(wx.EVT_BUTTON, self.on_create_resource)
        button_hyperlink_sizer.Add(create_button, 0, wx.ALIGN_LEFT)

        sizer.Add(button_hyperlink_sizer, 0, wx.ALL | wx.EXPAND, 10)
        
        sizer.Add(wx.StaticText(self, label="Or upload a CSV file to create multiple resources:"), flag=wx.ALL, border=5)
        csv_button = wx.Button(self, label="Create from CSV File")
        csv_button.Bind(wx.EVT_BUTTON, self.on_create_from_csv)
        sizer.Add(csv_button, 0, wx.ALL, 10)

        self.SetSizer(sizer)

    def on_create_resource(self, event):
        title = self.title_input.GetValue() or None
        resdef_id = self.resdef_id_input.GetValue() or None
        priority = self.priority_input.GetValue() or None
        qty = self.qty_input.GetValue() or None
        location = self.location_input.GetValue() or None
        category_id = self.category_input.GetValue() or None

        # Use ApplicationService for resource creation
        response = self.app_service.create_single_resource(
            title=title,
            resdef_id=resdef_id,
            priority=priority,
            quantity=qty,
            location=location,
            category_id=category_id
        )

        if response.is_success():
            if response.data:
                self.create_hyperlink(response.data[0]['res_link'])
        else:
            if response.error_code == "CATEGORY_ID_ERROR":
                self.handle_category_id_error(title, resdef_id, priority, qty, location)
            else:
                wx.MessageBox(response.message, "Error")

    def try_create_resource(self, title, resdef_id, priority, qty, location, category_id):
        """Attempt to create a resource with given parameters"""
        response = self.app_service.create_single_resource(
            title=title,
            resdef_id=resdef_id,
            priority=priority,
            quantity=qty,
            location=location,
            category_id=category_id
        )

        if response.is_success() and response.data:
            self.create_hyperlink(response.data[0]['res_link'])
        elif not response.is_success():
            raise Exception(response.message)

    def handle_category_id_error(self, title, resdef_id, priority, qty, location):
        """Handle the case where category ID is needed"""
        dialog = wx.TextEntryDialog(
            self,
            "ResdefID is invalid or not found.\nPlease enter a Category ID:",
            "Category ID Required"
        )
        
        if dialog.ShowModal() == wx.ID_OK:
            category_id = dialog.GetValue()
            dialog.Destroy()
            
            # Update the category ID field
            self.category_input.SetValue(category_id)
            
            # Try creating resource again with the provided category ID
            try:
                self.try_create_resource(title, resdef_id, priority, qty, location, category_id)
            except Exception as e:
                wx.MessageBox(str(e), "Resource Creation Error")
        else:
            dialog.Destroy()

    def on_create_from_csv(self, event):
        with wx.FileDialog(self, "Open CSV file", wildcard="CSV files (*.csv)|*.csv",
                           style=wx.FD_OPEN | wx.FD_FILE_MUST_EXIST) as fileDialog:
            if fileDialog.ShowModal() == wx.ID_CANCEL:
                return
            
            file_path = fileDialog.GetPath()
            self.start_create_thread(file_path)
            self.output_redirector.write('Resources Creation Starting..............\n')

    def start_create_thread(self, file_path):
        thread = threading.Thread(target=self.threaded_create, args=(file_path,))
        thread.daemon = True
        thread.start()

    def threaded_create(self, file_path):
        try:
            response = self.app_service.create_resources_from_csv(file_path)
            if response.is_success():
                self.output_redirector.write("Resource creation completed successfully.\n")
                if response.data:
                    wx.CallAfter(self.create_hyperlink, response.data[0]['res_link'])
            else:
                if response.error_code == "CATEGORY_ID_ERROR":
                    # Need to use CallAfter for GUI operations from a thread
                    wx.CallAfter(self.show_category_dialog_for_csv, file_path)
                else:
                    self.output_redirector.write(f"Error during resource creation: {response.message}\n")
        except Exception as e:
            self.output_redirector.write(f"Error during resource creation: {str(e)}\n")

    def show_category_dialog_for_csv(self, file_path):
        """Show dialog to get category ID for CSV creation"""
        dialog = wx.TextEntryDialog(
            self,
            "ResdefID is invalid or not found.\nPlease enter a Category ID:",
            "Category ID Required"
        )
        
        if dialog.ShowModal() == wx.ID_OK:
            category_id = dialog.GetValue()
            dialog.Destroy()
            
            # Try creating resources again with the provided category ID
            try:
                # Need to modify CSV to include category_id
                df = CSVHandler.read_csv(file_path)
                df['category_id'] = category_id
                temp_path = file_path + '.tmp'
                CSVHandler.write_csv(data, temp_path, index=False)
                
                response = self.app_service.create_resources_from_csv(temp_path)
                if response.is_success() and response.data:
                    self.create_hyperlink(response.data[0]['res_link'])
                
                # Clean up temp file
                os.remove(temp_path)
            except Exception as e:
                self.output_redirector.write(f"Error during resource creation: {str(e)}\n")
        else:
            dialog.Destroy()

    def create_hyperlink(self, res_id_link):
        if self.hyperlink_ctrl:
            self.hyperlink_ctrl.Destroy()
            
        self.hyperlink_ctrl = wx.adv.HyperlinkCtrl(self, label=res_id_link, url=res_id_link)
        button_hyperlink_sizer = self.GetSizer().GetChildren()[3].GetSizer()
        button_hyperlink_sizer.Add(self.hyperlink_ctrl, 0, wx.ALIGN_CENTER_HORIZONTAL | wx.ALIGN_CENTRE_HORIZONTAL, 10)
        self.Layout()
