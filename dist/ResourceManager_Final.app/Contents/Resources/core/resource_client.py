#!/usr/bin/env python

import json
import requests
import urllib3
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)


import core.resource_core as tsttresource_core
# from resource_search import build_search_criteria
from core.resource_core import ENV_PRODUCTION, ENV_UAT
from collections import namedtuple
from datetime import datetime, timedelta
from core.interfaces import ResourceClientInterface

RadarToken = namedtuple('RadarToken', ['token', 'expiration_datetime'])


class TSTTResourceClient(ResourceClientInterface):
    """Interface to abstract a connection the the TSTT Resource API

    This class takes care of transparently fetching an authentication token
    from Radar and refreshing it when it expires.
    Request methods return a dictionary or a list depending on the nature of the inquiry.
    When an ID has to be passed to a request, the ID's type is always int.

    Args:
        env (Optional[str]): specify what Radar environment to use (ENV_PRODUCTION or ENV_UAT)

    Attributes:
        radar_token (RadarToken): tuple containing a Radar authentication token
                                  value and expiration date
    """

    def __init__(self, env=None, radar_token=None, disable_verify_token=False):
        super(TSTTResourceClient, self).__init__()
        if env is not None and env in [ENV_PRODUCTION, ENV_UAT]:
            tsttresource_core.set_env(env)

        self.disable_verify_token = disable_verify_token

        if radar_token:
            self.radar_token = radar_token
        else:
            self.radar_token = None
            self.refresh_token()

    def refresh_token(self):
        """Get an authentication token from Radar and refresh internal state"""
        expiration_datetime = datetime.now()
        token, expires_in = tsttresource_core.get_authentication_token()
        expires_in = int(expires_in.split(' ')[0])
        expiration_datetime += timedelta(seconds=expires_in)
        self.radar_token = RadarToken(token, expiration_datetime)

    def verify_token(self):
        """Verify internal Radar token expiration date and refresh the token if necessary

        Note:
            You don't need to call this method, it's already done for every request.
        """
        if not self.disable_verify_token:
            if self.radar_token is None or self.radar_token.expiration_datetime < datetime.now():
                self.refresh_token()

    def _invoke_core_request_function(self, func, *args):
        """Verify the validity of the internal Radar token before calling a request function

        Note:
            A decorator is not used to perform the token verification because
            it shadows methods' signatures when calling `help()`.
            This method shouldn't be called outside of a core request function wrapper.

        Args:
            func (function): TSTT Resource request function; its last
                             parameter is expected to be a Radar token value
            *args (list): arguments to pass to `func`

        Returns:
            json: json data returned by the TSTT Resource API (either a list or a dict)
        """
        self.verify_token()
        
        return func(*args, token=self.radar_token.token)

    def request(self, uri_path, method, headers={}, body=None, timeout=60):
        """Perform a request to the TSTT Resource API

        Note:
            This method can be used to make a request on an endpoint that's not
            implemented and lets the caller handle the HTTP response.

        Args:
            uri_path (str): endpoint to make the request to; it shouldn't include the server URL
            method (str): HTTP method to use (GET or POST)
            headers (Optional[dict]): headers to use in the HTTP request
            body (Optional[dict]): body of the HTTP request

        Returns:
            httplib.HTTPResponse: response returned by the API server
        """
        return self._invoke_core_request_function(tsttresource_core.radar_request, uri_path, method, headers, body,
                                                  timeout)

    # def find_resources(self, search_data, sorting_order=None, max_results=1000, page_size=1000, page_number=0,
    #                    timeout=60):
    #     """Find resources that match a given search predicate

    #     Args:
    #         search_data (dict, list): search predicate suitable to be passed to
    #                                   tsttresource_search.build_search_criteria()
    #         sorting_order (Optional[list]): list of ordering preferences; defaults
    #                                         to ordering by id
    #         max_results (Optional[int]): maximum number of resources to return; defaults to 1000
    #         page_size (Optional[int]): The page size; defaults to 1000
    #         page_number (Optional[int]): The page number; defaults to 1
    #     Returns:
    #         list[dict]: list of found Resources
    #     """
    #     if sorting_order is None:
    #         sorting_order = [{'columnName': 'id', 'columnOrder': 1}]
    #     search_criteria = build_search_criteria(search_data)
    #     body = {
    #         'operation': 4,
    #         'sorting': sorting_order,
    #         'rowLimit': int(max_results),
    #         'pageSize': int(page_size),
    #         'pageNumber': int(page_number),
    #         'searchCriteria': search_criteria
    #     }
    #     response = self._invoke_core_request_function(tsttresource_core.find_resources, body, timeout)
    #     if 'resources' not in response['resource']:
    #         return []
    #     return response['resource']['resources']

    def find_scheduled_resources(self, search_data, sorting_order=None, max_results=1000, timeout=60):
        """Find scheduled resources that match a given search predicate

        Args:
            search_data (dict, list): search predicate suitable to be passed to
                                      tsttresource_search.build_search_criteria()
            sorting_order (Optional[list]): list of ordering preferences; defaults
                                            to ordering by id
            max_results (Optional[int]): maximum number of resources to return; defaults to 1000

        Returns:
            list[dict]: list of found Resources
        """
        if sorting_order is None:
            sorting_order = [{'columnName': 'id', 'columnOrder': 1}]
        search_criteria = build_search_criteria(search_data)
        body = {
            'operation': 4,
            'sorting': sorting_order,
            'rowLimit': max_results,
            'searchCriteria': search_criteria
        }
        response = self._invoke_core_request_function(tsttresource_core.find_scheduled_resources, body, timeout, )
        if 'schResourceList' not in response['scheduledResource']:
            return []
        return response['scheduledResource']['schResourceList']

    # def find_resource_definitions(self, search_data, sorting_order=None, max_results=1000, page_size=1000,
    #                               page_number=0, timeout=60):
    #     """Find resource definitions that match a given search predicate

    #     Args:
    #         search_data (dict, list): search predicate suitable to be passed to
    #                                   tsttresource_search.build_search_criteria()
    #         sorting_order (Optional[list]): list of ordering preferences; defaults
    #                                         to ordering by id
    #         max_results (Optional[int]): maximum number of resources to return; defaults to 1000
    #         page_size (Optional[int]): The page size; defaults to 1000
    #         page_number (Optional[int]): The page number; defaults to 1
    #     Returns:
    #         list[dict]: list of found Resources
    #     """
    #     if sorting_order is None:
    #         sorting_order = [{'columnName': 'id', 'columnOrder': 1}]
    #     search_criteria = build_search_criteria(search_data)
    #     body = {
    #         'operation': 4,
    #         'sorting': sorting_order,
    #         'rowLimit': int(max_results),
    #         'pageSize': int(page_size),
    #         'pageNumber': int(page_number),
    #         'searchCriteria': search_criteria
    #     }
    #     response = self._invoke_core_request_function(tsttresource_core.find_resource_definitions, body,
    #                                                   timeout)
    #     if 'resourceDefinition' not in response['resourceDefinition']:
    #         return []
    #     return response['resourceDefinition']['resourceDefinition']
    def find_resource_definitions(self, search_data, timeout=60):
        body = search_data
        response = self._invoke_core_request_function(tsttresource_core.find_resource_definitions, body, timeout)
        if response == []:
            return []
        # return response['resourceDefinition']['resourceDefinition']
        return response

    def find_scheduled_resource_definitions(self, search_data, sorting_order=None, max_results=1000, timeout=60):
        """Find scheduled resource definitions that match a given search predicate

        Args:
            search_data (dict, list): search predicate suitable to be passed to
                                      tsttresource_search.build_search_criteria()
            sorting_order (Optional[list]): list of ordering preferences; defaults
                                            to ordering by id
            max_results (Optional[int]): maximum number of resources to return; defaults to 1000

        Returns:
            list[dict]: list of found Resources
        """
        if sorting_order is None:
            sorting_order = [{'columnName': 'id', 'columnOrder': 1}]
        search_criteria = build_search_criteria(search_data)
        body = {
            'operation': 4,
            'sorting': sorting_order,
            'rowLimit': max_results,
            'searchCriteria': search_criteria
        }
        response = self._invoke_core_request_function(tsttresource_core.find_scheduled_resource_definitions, body,
                                                      timeout)
        if 'schResourceDefList' not in response['scheduledResourceDefinition']:
            return []
        return response['scheduledResourceDefinition']['schResourceDefList']

    def resource_for_id(self, resource_id, timeout=60):
        response = self._invoke_core_request_function(tsttresource_core.resource_for_id, resource_id, timeout)
        return response

    def resources_for_component(self, component_name, component_version, max_results=1000, page_size=1000,
                                page_number=0, timeout=60):
        """Fetch Resource Definitions that belong to a specified Radar component

        Note:
            Resources fetched using this method have less details than a
            Resource fetched using `resource_for_id()`.
            page_size (Optional[int]): The page size; defaults to 1000
            page_number (Optional[int]): The page number; defaults to 1
        Args:
            component_name (str): name of the component
            component_version (str): version of the component
            max_results (Optional[int]): maximum number of resources to return; defaults to 1000

        Returns:
            list[dict]: list of found Resources
        """
        search_query = {'componentHierarchy': {'name': component_name, 'version': component_version}}
        return self.find_resources(search_query, max_results=max_results, page_size=page_size,
                                   page_number=page_number, timeout=timeout)

    def resource_definitions_for_component(self, component_name, component_version, max_results=1000, page_size=1000,
                                           page_number=0, timeout=60):
        """Fetch Resources that belong to a specified Radar component

        Note:
            Resources fetched using this method have less details than a
            Resource fetched using `resource_for_id()`.
            page_size (Optional[int]): The page size; defaults to 1000
            page_number (Optional[int]): The page number; defaults to 1
        Args:
            component_name (str): name of the component
            component_version (str): version of the component
            max_results (Optional[int]): maximum number of resources to return; defaults to 1000

        Returns:
            list[dict]: list of found Resources
        """
        search_query = {'componentHierarchy': {'name': component_name, 'version': component_version}}
        return self.find_resource_definitions(search_query, max_results=max_results, page_size=page_size,
                                              page_number=page_number, timeout=timeout)

    # def keywords_for_resource_id(self, resource_id, timeout=60):
    #     response = self._invoke_core_request_function(tsttresource_core.resource_for_id, resource_id,
    #                                                   timeout)
    #     return response['keywords']

    # def names_for_resource_id(self, resource_id, timeout=60):
    #     response = self._invoke_core_request_function(tsttresource_core.resource_for_id, resource_id,
    #                                                   timeout)
    #     return response['title']

    def update_name_of_resource(self, resource_id, name_id, name_title, name_description, name_type, timeout=60):
        response = self._invoke_core_request_function(tsttresource_core.update_name_of_resource, resource_id, name_id,
                                                      name_title, name_description, name_type, timeout)
        return response

    def delete_name_of_resource(self, resource_id, name_id, timeout=60):
        response = self._invoke_core_request_function(tsttresource_core.delete_name_of_resource, resource_id, name_id,
                                                      timeout)
        return response

    # def numbers_for_resource_id(self, resource_id, timeout=60):
    #     response = self._invoke_core_request_function(tsttresource_core.resource_for_id, resource_id,
    #                                                   timeout)
    #     return response['numbers']

    def update_number_of_resource(self, resource_id, number_id, number_title, number_description, number_type,
                                  timeout=60):
        response = self._invoke_core_request_function(tsttresource_core.update_number_of_resource, resource_id,
                                                      number_id, number_title, number_description, number_type,
                                                      timeout)
        return response

    def delete_number_of_resource(self, resource_id, number_id, timeout=60):
        response = self._invoke_core_request_function(tsttresource_core.delete_number_of_resource, resource_id,
                                                      number_id, timeout)
        return response

    def fields_for_resource_id(self, resource_id, timeout=60, request_fields=[]):
        response = self._invoke_core_request_function(tsttresource_core.fields_for_resource_id, resource_id,request_fields,
                                                      timeout)
        return response

    # def related_problems_for_resource_id(self, resource_id, timeout=60):
    #     response = self._invoke_core_request_function(tsttresource_core.related_problems_for_resource_id, resource_id,
    #                                                   timeout)
                                                      
    #     return response['resource']['listData']['relatedProblems']

    # def related_scheduled_test_cases_for_resource_id(self, resource_id, timeout=60):
    #     response = self._invoke_core_request_function(tsttresource_core.related_scheduled_test_cases_for_resource_id,
    #                                                   resource_id, timeout)
    #     return response['resource']['listData']['relatedTests']

    # def other_related_items_for_resource_id(self, resource_id, timeout=60):
    #     response = self._invoke_core_request_function(tsttresource_core.other_related_items_for_resource_id,
    #                                                   resource_id, timeout)
    #     return response['resource']['listData']['otherRelatedItems']

    # def notes_for_resource_id(self, resource_id, note_type=1, timeout=60):
    #     """Fetch Notes data of a resource identified by `resource_id`

    #     Note:
    #         note_type can take note type values defined in constants.notes:
    #             - user_notes (default)
    #             - history_notes
    #             - all_notes
    #         Example:
    #             from tsttresource.tsttresource_client import TSTTResourceClient
    #             from tsttresource.constants import notes
    #             client = TSTTResourceClient()
    #             client.notes_for_resource_id(42, notes.all_notes)

    #     Args:
    #         resource_id (int): ID of the resource to fetch notes data from
    #         note_type (Optional[constants.notes]): kind of notes to fetch (see
    #                                                Note section above)

    #     Returns:
    #         list[dict]: list of notes
    #     """
    #     response = self._invoke_core_request_function(tsttresource_core.notes_for_resource_id, resource_id, note_type,
    #                                                   timeout)
    #     return response['resource']['notes']

    def fields_for_resource_definition_id(self, resource_id, request_fields=[], timeout=60):
        response = self._invoke_core_request_function(tsttresource_core.fields_for_resource_definition_id, resource_id,request_fields, timeout
                                                      )
        return response

    # def notes_for_resource_definition_id(self, resource_definition_id, note_type=1, timeout=60):
    #     """Fetch Notes data of a resource identified by `resource_definition_id`

    #     Note:
    #         note_type can take note type values defined in constants.notes:
    #             - user_notes (default)
    #             - history_notes
    #             - all_notes
    #         Example:
    #             from tsttresource.tsttresource_client import TSTTResourceClient
    #             from tsttresource.constants import notes
    #             client = TSTTResourceClient()
    #             client.notes_for_resource_definition_id(42, notes.all_notes)

    #     Args:
    #         resource_definition_id (int): ID of the resource to fetch notes data from
    #         note_type (Optional[constants.notes]): kind of notes to fetch (see
    #                                                Note section above)

    #     Returns:
    #         list[dict]: list of notes
    #     """
    #     response = self._invoke_core_request_function(tsttresource_core.notes_for_resource_definition_id,
    #                                                   resource_definition_id, note_type, timeout)
    #     return response['resourceDefinition']['notes']

    def resource_definition_for_id(self, resource_definition_id, timeout=60):
        response = self._invoke_core_request_function(tsttresource_core.resource_definition_for_id,
                                                      resource_definition_id, timeout)
        return response
    

    def keywords_for_resource_definition_id(self, resource_definition_id, timeout=60):
        response = self._invoke_core_request_function(tsttresource_core.keywords_for_resource_definition_id,
                                                      resource_definition_id, timeout)
        return response['resourceDefinition']['listData']['keywords']

    def related_problems_for_resource_definition_id(self, resource_definition_id, timeout=60):
        response = self._invoke_core_request_function(tsttresource_core.related_problems_for_resource_definition_id,
                                                      resource_definition_id, timeout)
        return response['resourceDefinition']['listData']['relatedProblems']

    def related_test_cases_for_resource_definition_id(self, resource_definition_id, timeout=60):
        response = self._invoke_core_request_function(tsttresource_core.related_test_cases_for_resource_definition_id,
                                                      resource_definition_id, timeout)
        return response['resourceDefinition']['listData']['relatedTests']

    def scheduled_resource_for_id(self, scheduled_resource_id, timeout=60):
        response = self._invoke_core_request_function(tsttresource_core.scheduled_resource_for_id,
                                                      scheduled_resource_id, timeout)
        return response['scheduledResource']

    def names_for_scheduled_resource_id(self, scheduled_resource_id, timeout=60):
        response = self._invoke_core_request_function(tsttresource_core.names_for_scheduled_resource_id,
                                                      scheduled_resource_id, timeout)
        return response['scheduledResource']['listData']['names']

    def numbers_for_scheduled_resource_id(self, scheduled_resource_id, timeout=60):
        response = self._invoke_core_request_function(tsttresource_core.numbers_for_scheduled_resource_id,
                                                      scheduled_resource_id, timeout)
        return response['scheduledResource']['listData']['numbers']

    def properties_for_scheduled_resource_id(self, scheduled_resource_id, timeout=60):
        response = self._invoke_core_request_function(tsttresource_core.properties_for_scheduled_resource_id,
                                                      scheduled_resource_id, timeout)
        return response['scheduledResource']['listData']['bundles']

    def keywords_for_scheduled_resource_id(self, scheduled_resource_id, timeout=60):
        response = self._invoke_core_request_function(tsttresource_core.keywords_for_scheduled_resource_id,
                                                      scheduled_resource_id, timeout)
        return response['scheduledResource']['listData']['keywords']

    def related_problems_for_scheduled_resource_id(self, scheduled_resource_id, timeout=60):
        response = self._invoke_core_request_function(tsttresource_core.related_problems_for_scheduled_resource_id,
                                                      scheduled_resource_id, timeout)
        return response['scheduledResource']['listData']['relatedProblems']

    def other_related_items_for_scheduled_resource_id(self, scheduled_resource_id, timeout=60):
        response = self._invoke_core_request_function(tsttresource_core.other_related_items_for_scheduled_resource_id,
                                                      scheduled_resource_id, timeout)
        return response['scheduledResource']['listData']['otherRelatedItems']

    def scheduled_resource_definition_for_id(self, scheduled_resource_definition_id, timeout=60):
        response = self._invoke_core_request_function(tsttresource_core.scheduled_resource_definition_for_id,
                                                      scheduled_resource_definition_id, timeout)
        return response['scheduledResourceDefinition']

    def keywords_for_scheduled_resource_definition_id(self, scheduled_resource_definition_id, timeout=60):
        response = self._invoke_core_request_function(tsttresource_core.keywords_for_scheduled_resource_definition_id,
                                                      scheduled_resource_definition_id, timeout)
        return response['scheduledResourceDefinition']['listData']['keywords']

    def related_problems_for_scheduled_resource_definition_id(self, scheduled_resource_definition_id, timeout=60):
        response = self._invoke_core_request_function(
            tsttresource_core.related_problems_for_scheduled_resource_definition_id, scheduled_resource_definition_id,
            timeout)
        return response['scheduledResourceDefinition']['listData']['relatedProblems']

    def related_test_cases_for_scheduled_resource_definition_id(self, scheduled_resource_definition_id, timeout=60):
        response = self._invoke_core_request_function(
            tsttresource_core.related_test_cases_for_scheduled_resource_definition_id, scheduled_resource_definition_id,
            timeout)
        return response['scheduledResourceDefinition']['listData']['relatedTests']

    def resources_for_test_suite_id(self, test_suite_id, timeout=60):
        response = self._invoke_core_request_function(tsttresource_core.resources_for_test_suite_id, test_suite_id,
                                                      timeout)
        return response['data']

    def resources_for_test_case_id(self, test_case_id, timeout=60):
        response = self._invoke_core_request_function(tsttresource_core.resources_for_test_case_id, test_case_id,
                                                      timeout)
        return response['data']

    def resources_for_scheduled_test_case_id(self, test_case_id, timeout=60):
        response = self._invoke_core_request_function(tsttresource_core.resources_for_scheduled_test_case_id,
                                                      test_case_id, timeout)
        return response['data']

    def resources_for_scheduled_test_id(self, scheduled_test_id, timeout=60):
        response = self._invoke_core_request_function(tsttresource_core.resources_for_scheduled_test_id,
                                                      scheduled_test_id, timeout)
        return response['data']

    def attach_res_to_scheduled_test_case(self, scheduled_test_case_id, res_ids, timeout=60):
        response = self._invoke_core_request_function(tsttresource_core.attach_res_to_scheduled_test_case,
                                                      scheduled_test_case_id,
                                                      res_ids, timeout)
        return response

    def attach_resdefs_to_scheduled_test_case(self, scheduled_test_case_id, resdef_ids, timeout=60):
        response = self._invoke_core_request_function(tsttresource_core.attach_resdefs_to_scheduled_test_case,
                                                      scheduled_test_case_id,
                                                      resdef_ids,
                                                      timeout)
        return response

    def remove_res_from_scheduled_test_case(self, scheduled_test_case_id, res_ids, timeout=60):
        response = self._invoke_core_request_function(tsttresource_core.remove_res_from_scheduled_test_case,
                                                      scheduled_test_case_id,
                                                      res_ids, timeout)
        return response

    def remove_resdefs_from_scheduled_test_case(self, scheduled_test_case_id, resdef_ids, timeout=60):
        response = self._invoke_core_request_function(tsttresource_core.remove_resdefs_from_scheduled_test_case,
                                                      scheduled_test_case_id,
                                                      resdef_ids, timeout)
        return response

    def attach_res_to_scheduled_test(self, scheduled_test_id, res_ids, timeout=60):
        response = self._invoke_core_request_function(tsttresource_core.attach_res_to_scheduled_test,
                                                      scheduled_test_id,
                                                      res_ids, timeout)
        return response

    def attach_resdefs_to_scheduled_test(self, scheduled_test_id, resdef_ids, timeout=60):
        response = self._invoke_core_request_function(tsttresource_core.attach_resdefs_to_scheduled_test,
                                                      scheduled_test_id,
                                                      resdef_ids, timeout)
        return response

    def remove_res_from_scheduled_test(self, scheduled_test_id, res_ids, timeout=60):
        response = self._invoke_core_request_function(tsttresource_core.remove_res_from_scheduled_test,
                                                      scheduled_test_id,
                                                      res_ids, timeout)
        return response

    def remove_resdefs_from_scheduled_test(self, scheduled_test_id, resdef_ids, timeout=60):
        response = self._invoke_core_request_function(tsttresource_core.remove_resdef_from_scheduled_test,
                                                      scheduled_test_id,
                                                      resdef_ids, timeout)
        return response

    def set_focus_scheduled_res_to_scheduled_test(self, scheduled_test_id, focus_type, scheduled_res_ids, timeout=60):
        response = self._invoke_core_request_function(tsttresource_core.set_focus_scheduled_res_to_scheduled_test,
                                                      scheduled_test_id,
                                                      focus_type,
                                                      scheduled_res_ids, timeout)
        return response

    def set_focus_scheduled_resdefs_to_scheduled_test(self, scheduled_test_id, focus_type, scheduled_resdef_ids,
                                                      timeout=60):
        response = self._invoke_core_request_function(tsttresource_core.set_focus_scheduled_resdefs_to_scheduled_test,
                                                      scheduled_test_id,
                                                      focus_type,
                                                      scheduled_resdef_ids, timeout)
        return response

    def create_resource(self, resource, timeout=60):
        response = self._invoke_core_request_function(tsttresource_core.create_resource,
                                                      resource, timeout)
        
        return response

    def update_resource(self, resource_id, resource, timeout=60):
        response = self._invoke_core_request_function(tsttresource_core.update_resource,
                                                      resource_id,
                                                      resource, timeout)
        return response

    def attach_keywords_to_resource(self, resource_id, keyword_ids, priority, timeout=60):
        response = self._invoke_core_request_function(tsttresource_core.attach_keywords_to_resource,
                                                      resource_id, keyword_ids, priority, timeout)
        return response

    def attach_name_to_resource(self, resource_id, name_title, name_description, name_type, timeout=60):
        response = self._invoke_core_request_function(tsttresource_core.attach_name_to_resource,
                                                      resource_id, name_title, name_description, name_type,
                                                      timeout)
        return response

    def attach_number_to_resource(self, resource_id, number_title, number_description, number_type, timeout=60):
        response = self._invoke_core_request_function(tsttresource_core.attach_number_to_resource,
                                                      resource_id, number_title, number_description, number_type,
                                                      timeout)
        return response

    def attach_property_to_resource(self, resource_id, item_id, item_name, title, description, type, timeout=60):
        response = self._invoke_core_request_function(tsttresource_core.attach_property_to_resource,
                                                      resource_id, item_id, item_name, title, description, type,
                                                      timeout)
        return response

    def update_property_of_resource(self, resource_id, item_id, name_id, item_name, title, description, type,
                                    timeout=60):
        response = self._invoke_core_request_function(tsttresource_core.update_property_of_resource,
                                                      resource_id, item_id, name_id, item_name, title, description,
                                                      type, timeout)
        return response

    def delete_property_of_resource(self, resource_id, item_id, name_id, timeout=60):
        response = self._invoke_core_request_function(tsttresource_core.delete_property_of_resource,
                                                      resource_id, item_id, name_id, timeout)
        return response

    def create_resource_definition(self, resource_definition, timeout=60):
        response = self._invoke_core_request_function(tsttresource_core.create_resource_definition,
                                                      resource_definition, timeout)
        return response

    def update_resource_definition(self, resource_definition_id, resource_definition, timeout=60):
        response = self._invoke_core_request_function(tsttresource_core.update_resource_definition,
                                                      resource_definition_id,
                                                      resource_definition, timeout)
        return response

    def attach_keywords_to_resource_definition(self, resource_definition_id, keyword_ids, priority, timeout=60):
        response = self._invoke_core_request_function(tsttresource_core.attach_keywords_to_resource_definition,
                                                      resource_definition_id, keyword_ids, priority, timeout)
        return response
