"""
Application Service Layer for Resource Manager.

This module provides a service layer that acts as an intermediary between
the GUI and the business logic, implementing the Application Service pattern.
"""

from typing import List, Dict, Any, Optional, Callable
from core.resource_manager import ResourceManager
from core.resource_client import TSTTResourceClient
from core.response_models import ApiResponse
from core.exceptions import ResourceManagerException, ValidationError
from utils.file_path_manager import file_path_manager


class ApplicationService:
    """
    Application Service that coordinates between GUI and business logic.
    
    This service provides a clean interface for GUI components and handles
    all business logic coordination, error handling, and data transformation.
    """
    
    def __init__(self, resource_client: TSTTResourceClient = None):
        """
        Initialize ApplicationService.
        
        Args:
            resource_client: Optional resource client instance
        """
        self.resource_manager = ResourceManager(resource_client)
        self._observers = []
    
    def add_observer(self, observer: Callable[[str, Any], None]) -> None:
        """
        Add an observer for status updates.
        
        Args:
            observer: Callback function that receives (event_type, data)
        """
        self._observers.append(observer)
    
    def remove_observer(self, observer: Callable[[str, Any], None]) -> None:
        """Remove an observer."""
        if observer in self._observers:
            self._observers.remove(observer)
    
    def _notify_observers(self, event_type: str, data: Any = None) -> None:
        """Notify all observers of an event."""
        for observer in self._observers:
            try:
                observer(event_type, data)
            except Exception:
                # Don't let observer errors break the service
                pass
    
    def create_single_resource(self, title: Optional[str] = None, 
                              resdef_id: Optional[str] = None,
                              priority: Optional[str] = None, 
                              quantity: Optional[str] = None,
                              location: Optional[str] = None, 
                              category_id: Optional[str] = None) -> ApiResponse[List[Dict[str, Any]]]:
        """
        Create a single resource.
        
        Args:
            title: Resource title
            resdef_id: Resource definition ID
            priority: Priority level
            quantity: Number of resources to create
            location: Location
            category_id: Category ID
            
        Returns:
            ApiResponse containing the created resources
        """
        try:
            self._notify_observers("resource_creation_started", {
                "title": title,
                "quantity": quantity
            })
            
            result = self.resource_manager.create_resource(
                title=title,
                resdef_id=resdef_id,
                priority=priority,
                quantity=quantity,
                location=location,
                category_id=category_id
            )
            
            self._notify_observers("resource_creation_completed", {
                "count": len(result),
                "resources": result
            })
            
            return ApiResponse.success(
                data=result,
                message=f"Successfully created {len(result)} resource(s)",
                metadata={"operation": "create_single", "count": len(result)}
            )
            
        except ValidationError as e:
            self._notify_observers("validation_error", {"error": str(e)})
            return ApiResponse.error(
                message=str(e),
                error_code=e.error_code
            )
        except ResourceManagerException as e:
            self._notify_observers("operation_error", {"error": str(e)})
            return ApiResponse.error(
                message=str(e),
                error_code=e.error_code
            )
        except Exception as e:
            self._notify_observers("unexpected_error", {"error": str(e)})
            return ApiResponse.error(
                message=f"Unexpected error: {str(e)}"
            )
    
    def create_resources_from_csv(self, csv_path: str) -> ApiResponse[List[Dict[str, Any]]]:
        """
        Create resources from CSV file.
        
        Args:
            csv_path: Path to CSV file
            
        Returns:
            ApiResponse containing the created resources
        """
        try:
            # Validate file exists
            if not file_path_manager.file_exists(csv_path):
                return ApiResponse.error(
                    message=f"CSV file not found: {csv_path}",
                    error_code="FILE_NOT_FOUND"
                )
            
            self._notify_observers("csv_creation_started", {"file": csv_path})
            
            result = self.resource_manager.create_resources_from_csv(csv_path)
            
            self._notify_observers("csv_creation_completed", {
                "count": len(result),
                "file": csv_path
            })
            
            return ApiResponse.success(
                data=result,
                message=f"Successfully processed CSV file and created {len(result)} resource(s)",
                metadata={"operation": "create_from_csv", "file": csv_path, "count": len(result)}
            )
            
        except Exception as e:
            self._notify_observers("operation_error", {"error": str(e)})
            return ApiResponse.error(
                message=f"Error processing CSV file: {str(e)}"
            )
    
    def update_resources_from_csv(self, csv_path: str) -> ApiResponse[List[Dict[str, Any]]]:
        """
        Update resources from CSV file.

        Args:
            csv_path: Path to CSV file

        Returns:
            ApiResponse containing the updated resources
        """
        try:
            # Validate file exists
            if not file_path_manager.file_exists(csv_path):
                return ApiResponse.error(
                    message=f"CSV file not found: {csv_path}",
                    error_code="FILE_NOT_FOUND"
                )

            self._notify_observers("csv_update_started", {"file": csv_path})

            result = self.resource_manager.update_resources(csv_path)

            self._notify_observers("csv_update_completed", {
                "count": len(result),
                "file": csv_path
            })

            return ApiResponse.success(
                data=result,
                message=f"Successfully updated {len(result)} resource(s) from CSV",
                metadata={"operation": "update_from_csv", "file": csv_path, "count": len(result)}
            )

        except Exception as e:
            self._notify_observers("operation_error", {"error": str(e)})
            return ApiResponse.error(
                message=f"Error updating resources from CSV: {str(e)}"
            )

    def update_resources(self, csv_path: str) -> ApiResponse[List[Dict[str, Any]]]:
        """
        Alias for update_resources_from_csv for backward compatibility.

        Args:
            csv_path: Path to CSV file

        Returns:
            ApiResponse containing the updated resources
        """
        return self.update_resources_from_csv(csv_path)
    
    def print_resources(self, file_path: Optional[str] = None) -> ApiResponse[str]:
        """
        Print resources to CSV file.
        
        Args:
            file_path: Optional input file path
            
        Returns:
            ApiResponse containing the output file path
        """
        try:
            self._notify_observers("print_started", {"input_file": file_path})
            
            output_file = self.resource_manager.print_resources(file_path)
            
            self._notify_observers("print_completed", {"output_file": output_file})
            
            return ApiResponse.success(
                data=output_file,
                message=f"Successfully generated print file: {output_file}",
                metadata={"operation": "print", "output_file": output_file}
            )
            
        except Exception as e:
            self._notify_observers("operation_error", {"error": str(e)})
            return ApiResponse.error(
                message=f"Error generating print file: {str(e)}"
            )
    
    def get_resource_count(self) -> ApiResponse[Dict[str, int]]:
        """
        Get resource count statistics.
        
        Returns:
            ApiResponse containing resource count data
        """
        try:
            # This would typically query the API for actual counts
            # For now, return mock data
            count_data = {
                "total_resources": 0,
                "active_resources": 0,
                "pending_resources": 0,
                "completed_resources": 0
            }
            
            return ApiResponse.success(
                data=count_data,
                message="Resource count retrieved successfully",
                metadata={"operation": "get_count"}
            )
            
        except Exception as e:
            return ApiResponse.error(
                message=f"Error retrieving resource count: {str(e)}"
            )
    
    def validate_csv_file(self, file_path: str, operation_type: str) -> ApiResponse[bool]:
        """
        Validate CSV file format.
        
        Args:
            file_path: Path to CSV file
            operation_type: Type of operation (create, update)
            
        Returns:
            ApiResponse indicating validation result
        """
        try:
            if not file_path_manager.file_exists(file_path):
                return ApiResponse.error(
                    message=f"File not found: {file_path}",
                    error_code="FILE_NOT_FOUND"
                )
            
            # Define required columns for different operations
            required_columns = {
                "create": ["Title*", "Qty", "Pri"],
                "update": ["resourceID*", "Title*", "Pri"]
            }
            
            if operation_type not in required_columns:
                return ApiResponse.error(
                    message=f"Unknown operation type: {operation_type}",
                    error_code="VALIDATION_ERROR"
                )
            
            # Here you would implement actual CSV validation
            # For now, just return success
            return ApiResponse.success(
                data=True,
                message="CSV file validation passed",
                metadata={"operation": "validate_csv", "file": file_path, "type": operation_type}
            )
            
        except Exception as e:
            return ApiResponse.error(
                message=f"Error validating CSV file: {str(e)}"
            )


# Global application service instance
_app_service = None

def get_application_service(resource_client: TSTTResourceClient = None) -> ApplicationService:
    """Get or create the global application service instance."""
    global _app_service
    if _app_service is None:
        _app_service = ApplicationService(resource_client)
    return _app_service
