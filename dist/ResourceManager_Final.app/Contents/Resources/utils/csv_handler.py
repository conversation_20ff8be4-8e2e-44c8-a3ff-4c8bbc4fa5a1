"""
CSV Handler Module - Lightweight replacement for pandas CSV operations
"""

import csv
import json
from pathlib import Path
from typing import Dict, List, Any, Optional, Union


class CSVHandler:
    """Lightweight CSV handler to replace pandas functionality."""
    
    @staticmethod
    def read_csv(filepath: Union[str, Path]) -> List[Dict[str, Any]]:
        """Read CSV file and return list of dictionaries."""
        filepath = Path(filepath)
        
        if not filepath.exists():
            raise FileNotFoundError(f"CSV file not found: {filepath}")
        
        try:
            data = []
            with open(filepath, 'r', encoding='utf-8', newline='') as file:
                # Detect delimiter
                sample = file.read(1024)
                file.seek(0)
                
                sniffer = csv.Sniffer()
                delimiter = sniffer.sniff(sample).delimiter
                
                reader = csv.DictReader(file, delimiter=delimiter)
                
                # Get all fieldnames for consistent row structure
                fieldnames = reader.fieldnames

                for row in reader:
                    # Create a list with proper indexing (like pandas)
                    row_list = []
                    for field in fieldnames:
                        value = row.get(field, '')
                        clean_value = str(value).strip() if value is not None else ''

                        # Keep all values as strings to match pandas behavior
                        # This prevents issues with .replace() calls on integers
                        row_list.append(clean_value)

                    data.append(row_list)

                # Insert header as first row (to match pandas behavior)
                if data:
                    data.insert(0, fieldnames)
            
            return data
            
        except Exception as e:
            raise Exception(f"Error reading CSV file {filepath}: {str(e)}")
    
    @staticmethod
    def write_csv(data: List[Any], filepath: Union[str, Path],
                  delimiter: str = ',') -> None:
        """Write data to CSV file. Supports both list of lists and list of dicts."""
        if not data:
            raise ValueError("No data to write")

        filepath = Path(filepath)
        filepath.parent.mkdir(parents=True, exist_ok=True)

        try:
            with open(filepath, 'w', encoding='utf-8', newline='') as file:
                writer = csv.writer(file, delimiter=delimiter)

                # Handle list of lists format (pandas-like)
                if isinstance(data[0], list):
                    for row in data:
                        # Convert all values to strings
                        clean_row = [str(value) if value is not None else '' for value in row]
                        writer.writerow(clean_row)

                # Handle list of dictionaries format
                elif isinstance(data[0], dict):
                    # Get all unique keys from all dictionaries
                    fieldnames = set()
                    for row in data:
                        fieldnames.update(row.keys())
                    fieldnames = sorted(list(fieldnames))

                    dict_writer = csv.DictWriter(file, fieldnames=fieldnames, delimiter=delimiter)
                    dict_writer.writeheader()

                    for row in data:
                        clean_row = {}
                        for key in fieldnames:
                            value = row.get(key, '')
                            if value is None:
                                clean_row[key] = ''
                            else:
                                clean_row[key] = str(value)
                        dict_writer.writerow(clean_row)
                else:
                    raise ValueError("Data must be a list of lists or list of dictionaries")

        except Exception as e:
            raise Exception(f"Error writing CSV file {filepath}: {str(e)}")
    
    @staticmethod
    def _is_float(value: str) -> bool:
        """Check if string represents a float number."""
        try:
            float(value)
            return '.' in value
        except ValueError:
            return False
