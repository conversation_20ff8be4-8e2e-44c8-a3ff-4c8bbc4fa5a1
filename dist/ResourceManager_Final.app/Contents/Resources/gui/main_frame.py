import wx
import sys
from gui.panels.create_panel import CreatePanel
from gui.panels.update_panel import UpdatePanel
from gui.panels.print_panel import PrintPanel
from utils.output_redirector import OutputRedirector
from core.application_service import get_application_service

class MainFrame(wx.Frame):
    def __init__(self, parent, resource_manager):
        super().__init__(parent, title="Resource Manager", size=(800, 700))
        self.resource_manager = resource_manager

        # Initialize Application Service
        self.app_service = get_application_service(resource_manager.resource_client)
        self.app_service.add_observer(self.on_application_event)

        # Redirect sys.stdout to log_output
        sys.stdout = OutputRedirector(self.log_output)

        # Create the main panel and notebook
        self.main_panel = wx.Panel(self)
        self.main_sizer = wx.BoxSizer(wx.VERTICAL)
        
        # Output text area - reduced size for cleaner interface
        self.output_text = wx.TextCtrl(self.main_panel, style=wx.TE_MULTILINE | wx.TE_READONLY | wx.HSCROLL)
        self.main_sizer.Add(self.output_text, 0, wx.EXPAND | wx.ALL, 10)
        self.output_text.SetMinSize((-1, 200))

        self.output_redirector = OutputRedirector(self.log_output)
        
        # Notebook setup
        self.notebook = wx.Notebook(self.main_panel)
        self.main_sizer.Add(self.notebook, 1, wx.EXPAND)

        # Create panels for each tab
        self.create_panel = CreatePanel(self.notebook, self.output_redirector, self.app_service)
        self.update_panel = UpdatePanel(self.notebook, self.output_redirector, self.app_service)
        self.print_panel = PrintPanel(self.notebook, self.app_service)

        self.notebook.AddPage(self.create_panel, "Resource Create")
        self.notebook.AddPage(self.update_panel, "Resource Update")
        self.notebook.AddPage(self.print_panel, "Resource Print")

        self.notebook.Bind(wx.EVT_NOTEBOOK_PAGE_CHANGED, self.on_page_changed)

        self.main_panel.SetSizer(self.main_sizer)
        self.Show()

    def log_output(self, message):
        """Appends text to the output_text widget."""
        self.output_text.AppendText(message)

    def on_application_event(self, event_type, data):
        """Handle application service events with simplified logging."""
        try:
            if event_type == "resource_creation_completed":
                self.log_output(f"✅ Created {data.get('count', 0)} resources\n")
            elif event_type == "csv_creation_completed":
                self.log_output(f"✅ Processed CSV: {data.get('count', 0)} resources created\n")
            elif event_type == "print_completed":
                # Extract just the filename for cleaner output
                output_file = data.get('output_file', '')
                if output_file:
                    from pathlib import Path
                    filename = Path(output_file).name
                    self.log_output(f"✅ Print file: {filename}\n")
            elif event_type == "validation_error":
                self.log_output(f"❌ Error: {data.get('error', 'Unknown error')}\n")
            elif event_type == "operation_error":
                self.log_output(f"❌ Error: {data.get('error', 'Unknown error')}\n")
            elif event_type == "unexpected_error":
                self.log_output(f"❌ Error: {data.get('error', 'Unknown error')}\n")
        except Exception as e:
            # Don't let event handling errors break the GUI
            self.log_output(f"❌ Event error: {str(e)}\n")
    
    def on_page_changed(self, event):
        """Handle page change events."""
        # Keep consistent output text size for all tabs
        self.output_text.SetMinSize((-1, 200))  # Reduced size for cleaner interface

        self.main_panel.GetSizer().Layout()
        self.main_panel.Refresh()
        event.Skip()  # Ensure other event handlers run
