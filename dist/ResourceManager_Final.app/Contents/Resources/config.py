"""
Configuration management for Resource Manager application.

This module centralizes all configuration settings including:
- API endpoints and timeouts
- File paths and directories
- Default values and constants
- Environment-specific settings
"""

import os
from pathlib import Path


class Config:
    """Central configuration class for the Resource Manager application."""
    
    # API Configuration
    API_BASE_URL = "https://api.example.com"  # Replace with actual API URL
    API_TIMEOUT = 60
    API_VERSION = "2.2"
    
    # Environment Configuration
    ENV_PRODUCTION = "production"
    ENV_UAT = "uat"
    ENV_DEVELOPMENT = "development"
    
    # Default Environment
    DEFAULT_ENV = ENV_PRODUCTION
    
    # File Paths Configuration
    PROJECT_ROOT = Path(__file__).parent.absolute()
    CORE_DIR = PROJECT_ROOT / "core"
    GUI_DIR = PROJECT_ROOT / "gui"
    UTILS_DIR = PROJECT_ROOT / "utils"
    PRINT_OUTPUT_DIR = PROJECT_ROOT / "print_output"
    
    # CSV File Names
    RESOURCE_CREATE_CSV = "resource_create.csv"
    RESOURCE_UPDATE_CSV = "resource_update.csv"
    RESOURCE_DUP_CSV = "resource_dup.csv"
    RESOURCE_UPDATE_TEMPLATE_CSV = "resource_update_template.csv"
    
    # Default Resource Values
    DEFAULT_COMPONENT_ID = 1118899
    DEFAULT_DRI_ID = 973776146
    DEFAULT_INVENTORY_KEEPER_ID = 973776146
    DEFAULT_CLASS_ID = 1
    DEFAULT_STATE_ID = 1
    DEFAULT_LOCATION_ID = 66842
    DEFAULT_SPECIFIC_LOCATION = "Aruba"
    DEFAULT_ASSIGNEE_ID = 1555131384
    
    # Priority Configuration
    PRIORITY_MIN = 0
    PRIORITY_MAX = 5
    PRIORITY_DEFAULT = 5
    PRIORITY_NA_VALUE = 5
    
    # GUI Configuration
    MAIN_WINDOW_SIZE = (800, 700)
    OUTPUT_TEXT_MIN_HEIGHT = 300
    OUTPUT_TEXT_MIN_HEIGHT_RESOURCE_COUNT = 200
    
    # Logging Configuration
    LOG_LEVEL = "DEBUG"
    LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    LOG_FILE = "app_init.log"
    
    @classmethod
    def get_env(cls) -> str:
        """Get current environment from environment variable or default."""
        return os.getenv('RESOURCE_MANAGER_ENV', cls.DEFAULT_ENV)
    
    @classmethod
    def get_api_base_url(cls) -> str:
        """Get API base URL based on current environment."""
        env = cls.get_env()
        if env == cls.ENV_UAT:
            return os.getenv('API_BASE_URL_UAT', cls.API_BASE_URL)
        elif env == cls.ENV_DEVELOPMENT:
            return os.getenv('API_BASE_URL_DEV', cls.API_BASE_URL)
        else:
            return os.getenv('API_BASE_URL_PROD', cls.API_BASE_URL)
    
    @classmethod
    def get_api_timeout(cls) -> int:
        """Get API timeout from environment or default."""
        return int(os.getenv('API_TIMEOUT', cls.API_TIMEOUT))
    
    @classmethod
    def ensure_directories(cls) -> None:
        """Ensure all required directories exist."""
        directories = [
            cls.PRINT_OUTPUT_DIR,
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
    
    @classmethod
    def get_csv_file_path(cls, filename: str) -> Path:
        """Get full path for CSV files in core directory."""
        return cls.CORE_DIR / filename
    
    @classmethod
    def get_output_file_path(cls, filename: str) -> Path:
        """Get full path for output files in print_output directory."""
        cls.ensure_directories()
        return cls.PRINT_OUTPUT_DIR / filename


class DatabaseConfig:
    """Database configuration (if needed in future)."""
    pass


class SecurityConfig:
    """Security-related configuration."""
    
    # SSL Configuration
    VERIFY_SSL = False  # Set to True in production
    
    # Token Configuration
    TOKEN_REFRESH_THRESHOLD = 300  # Refresh token 5 minutes before expiry


class FeatureFlags:
    """Feature flags for enabling/disabling functionality."""
    
    ENABLE_LOGGING = True
    ENABLE_DEBUG_MODE = False
    ENABLE_AUTO_BACKUP = True
    ENABLE_PROGRESS_TRACKING = True


# Environment-specific configurations
class DevelopmentConfig(Config):
    """Development environment configuration."""
    pass


class ProductionConfig(Config):
    """Production environment configuration."""
    pass


class UATConfig(Config):
    """UAT environment configuration."""
    pass


def get_config() -> Config:
    """Get configuration based on current environment."""
    env = Config.get_env()
    
    if env == Config.ENV_DEVELOPMENT:
        return DevelopmentConfig()
    elif env == Config.ENV_UAT:
        return UATConfig()
    else:
        return ProductionConfig()


# Global config instance
config = get_config()
