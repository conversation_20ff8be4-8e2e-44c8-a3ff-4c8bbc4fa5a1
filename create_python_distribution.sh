#!/bin/bash

# Resource Manager Python Distribution Package Creator
# This script creates a complete distribution package for the Python version

set -e  # Exit on any error

# Configuration
PACKAGE_NAME="resource_manager_python"
VERSION="1.0.0"
DIST_DIR="dist_python"
PACKAGE_DIR="${DIST_DIR}/${PACKAGE_NAME}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Clean previous builds
clean_previous() {
    print_status "Cleaning previous builds..."
    
    if [ -d "$DIST_DIR" ]; then
        rm -rf "$DIST_DIR"
    fi
    
    if [ -d "build" ]; then
        rm -rf "build"
    fi
    
    # Clean Python cache
    find . -type d -name "__pycache__" -exec rm -rf {} + 2>/dev/null || true
    find . -name "*.pyc" -delete 2>/dev/null || true
    find . -name "*.pyo" -delete 2>/dev/null || true
    
    print_success "Cleaned previous builds"
}

# Create distribution directory structure
create_structure() {
    print_status "Creating distribution directory structure..."
    
    mkdir -p "$PACKAGE_DIR"
    mkdir -p "$PACKAGE_DIR/source"
    mkdir -p "$PACKAGE_DIR/templates"
    mkdir -p "$PACKAGE_DIR/documentation"
    mkdir -p "$PACKAGE_DIR/scripts"
    
    print_success "Directory structure created"
}

# Copy source files
copy_source() {
    print_status "Copying source files..."
    
    # Copy main application files (excluding PyInstaller spec)
    cp main.py "$PACKAGE_DIR/"
    cp config.py "$PACKAGE_DIR/"
    cp requirements_macos.txt "$PACKAGE_DIR/"
    cp setup.py "$PACKAGE_DIR/"
    cp MANIFEST.in "$PACKAGE_DIR/"
    
    # Copy source directories
    cp -r core "$PACKAGE_DIR/"
    cp -r gui "$PACKAGE_DIR/"
    cp -r utils "$PACKAGE_DIR/"

    # Copy assets if they exist
    if [ -d "assets" ]; then
        cp -r assets "$PACKAGE_DIR/"
    fi
    
    # Copy templates
    if [ -d "templates" ]; then
        cp -r templates/* "$PACKAGE_DIR/templates/" 2>/dev/null || true
    fi
    
    # Copy CSV templates from core
    cp core/*.csv "$PACKAGE_DIR/templates/" 2>/dev/null || true
    
    print_success "Source files copied"
}

# Copy documentation
copy_documentation() {
    print_status "Copying documentation..."
    
    cp README_PYTHON.md "$PACKAGE_DIR/documentation/"
    cp DEVELOPMENT.md "$PACKAGE_DIR/documentation/"
    
    # Create additional documentation if it doesn't exist
    if [ ! -f "CHANGELOG.md" ]; then
        cat > "$PACKAGE_DIR/documentation/CHANGELOG.md" << 'EOF'
# Changelog

## [1.0.0] - 2024-01-01

### Added
- Initial release of Resource Manager Python application
- Resource creation and update functionality
- CSV import/export capabilities
- GUI interface with wxPython
- API integration with TSTT Resource API
- Kerberos authentication support
- Comprehensive error handling
- File path management utilities
- Print/export functionality

### Features
- Single and batch resource creation
- Resource updates from CSV files
- Template generation for CSV formats
- Real-time status updates
- Hyperlink generation for created resources
- Debug logging capabilities
- Cross-platform compatibility (macOS focused)

### Technical
- Clean architecture with dependency injection
- Standardized API response handling
- Comprehensive test coverage
- PyInstaller build support
- Distribution package creation
EOF
    else
        cp CHANGELOG.md "$PACKAGE_DIR/documentation/"
    fi
    
    print_success "Documentation copied"
}

# Copy installation scripts
copy_scripts() {
    print_status "Copying installation scripts..."
    
    cp install.sh "$PACKAGE_DIR/scripts/"
    
    # Make scripts executable
    chmod +x "$PACKAGE_DIR/scripts/"*.sh
    
    print_success "Installation scripts copied"
}

# Skip standalone application build
build_application() {
    print_status "Skipping standalone application build (source-only package)..."
    print_success "Source-only package - no standalone app needed"
}

# Create installation instructions
create_instructions() {
    print_status "Creating installation instructions..."
    
    cat > "$PACKAGE_DIR/INSTALLATION.txt" << 'EOF'
Resource Manager Python - Installation Instructions
==================================================

SYSTEM REQUIREMENTS:
- macOS 10.14 (Mojave) or later
- Python 3.7 or later
- Internet connection for API operations
- Apple Connect credentials with Kerberos setup

INSTALLATION OPTIONS:

Option 1: Automated Installation (Recommended)
1. Open Terminal and navigate to the package directory
2. Run the installation script:
   ./scripts/install.sh
3. Follow the on-screen instructions

Option 2: Manual Installation
1. Create virtual environment:
   python3 -m venv venv
2. Activate virtual environment:
   source venv/bin/activate
3. Install dependencies:
   pip install -r requirements_macos.txt
4. Run the application:
   python main.py

AUTHENTICATION SETUP:
Before first use, set up Kerberos authentication:
1. Open Terminal
2. Run: kinit <EMAIL>
3. Enter your Apple Connect password when prompted

USAGE:
1. Launch the application
2. Use the tabs to navigate between different functions:
   - Create Resource: Create single or multiple resources
   - Update Resources: Update existing resources from CSV
   - Print Resources: Generate reports
   - Resource Count: View statistics

TROUBLESHOOTING:
- If authentication fails, refresh Kerberos tickets with kinit
- For GUI issues, ensure wxPython is properly installed
- Check ~/Documents/ResourceManager_app.log for error details
- Enable debug mode: DEBUG_LOGGING=1 python main.py

SUPPORT:
For internal Apple support, contact the development team.
EOF
    
    print_success "Installation instructions created"
}

# Create package information
create_package_info() {
    print_status "Creating package information..."
    
    cat > "$PACKAGE_DIR/PACKAGE_INFO.txt" << EOF
Resource Manager Python Distribution Package
===========================================

Package: ${PACKAGE_NAME}
Version: ${VERSION}
Build Date: $(date)
Platform: macOS (Universal)
Python Version: 3.7+

CONTENTS:
- Complete Python source code
- templates/: CSV templates for resource operations
- documentation/: User and developer documentation
- scripts/: Installation and utility scripts

FEATURES:
- Resource creation and management
- CSV import/export functionality
- GUI interface with wxPython
- API integration with Apple TSTT Resource API
- Kerberos authentication
- Comprehensive error handling
- Debug logging capabilities

ARCHITECTURE:
- Clean architecture with separation of concerns
- Dependency injection for testability
- Standardized API response handling
- Modular design for maintainability

DEPENDENCIES:
- wxPython (GUI framework)
- requests (HTTP client)
- pykerberos (Authentication)
- Standard Python libraries

BUILD INFORMATION:
- Built on: $(sw_vers -productName) $(sw_vers -productVersion)
- Python: $(python3 --version)
- Architecture: $(uname -m)

For more information, see documentation/README_PYTHON.md
EOF
    
    print_success "Package information created"
}

# Create archive
create_archive() {
    print_status "Creating distribution archive..."

    cd "$DIST_DIR"

    # Create ZIP archive with the specified name
    zip -r "resource_manager_python.zip" "${PACKAGE_NAME}/" -x "*.DS_Store" "*/.*"

    cd ..

    print_success "Distribution archive created: resource_manager_python.zip"
}

# Generate checksums
generate_checksums() {
    print_status "Generating checksums..."

    cd "$DIST_DIR"

    # Generate SHA256 checksums
    shasum -a 256 resource_manager_python.zip > "resource_manager_python_checksums.txt"

    cd ..

    print_success "Checksums generated"
}

# Main distribution process
main() {
    echo "=========================================="
    echo "Resource Manager Python Distribution"
    echo "Package: resource_manager_python.zip"
    echo "=========================================="
    echo
    
    clean_previous
    create_structure
    copy_source
    copy_documentation
    copy_scripts
    create_instructions
    create_package_info
    create_archive
    generate_checksums
    
    echo
    echo "=========================================="
    print_success "Distribution package created successfully!"
    echo "=========================================="
    echo
    print_status "Package location: $PACKAGE_DIR"
    print_status "Archive created:"
    echo "  - ${DIST_DIR}/resource_manager_python.zip"
    echo
    print_status "Package contents:"
    echo "  - Complete Python source code"
    echo "  - Documentation and guides"
    echo "  - Installation scripts"
    echo "  - CSV templates"
    echo
    print_status "Next steps:"
    echo "  1. Test the package on a clean macOS system"
    echo "  2. Verify all functionality works correctly"
    echo "  3. Distribute to users"
    echo
}

# Run main function
main "$@"
